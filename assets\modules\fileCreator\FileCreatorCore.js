/**
 * File Creator Core - Advanced File Generation System
 * Creates professional PDF, PowerPoint, EXE files and more
 * Integrated with internet capabilities like ChatGPT Pro
 */

class FileCreatorCore {

    constructor() {
        this.isActive = false;
        this.internetAccess = true;
        this.creationHistory = [];
        this.templates = this.initTemplates();
        this.supportedFormats = [
            'pdf', 'pptx', 'docx', 'xlsx', 'exe', 'html', 'css', 'js', 
            'py', 'java', 'cpp', 'zip', 'json', 'xml', 'csv'
        ];
        this.internetTools = this.initInternetTools();
    }

    // Initialize templates for different file types
    initTemplates() {
        return {
            pdf: {
                report: 'تقرير احترافي',
                presentation: 'عرض تقديمي',
                manual: 'دليل المستخدم',
                invoice: 'فاتورة',
                certificate: 'شهادة',
                resume: 'سيرة ذاتية'
            },
            powerpoint: {
                business: 'عرض أعمال',
                educational: 'عرض تعليمي',
                technical: 'عرض تقني',
                marketing: 'عرض تسويقي',
                scientific: 'عرض علمي'
            },
            exe: {
                utility: 'أداة مساعدة',
                game: 'لعبة بسيطة',
                calculator: 'آلة حاسبة',
                converter: 'محول ملفات',
                organizer: 'منظم ملفات'
            }
        };
    }

    // Initialize internet tools
    initInternetTools() {
        return {
            imageSearch: true,
            dataFetching: true,
            apiAccess: true,
            downloadCapability: true,
            realTimeInfo: true
        };
    }

    // Activate File Creator Mode
    activate() {
        this.isActive = true;
        console.log('📁 File Creator Mode activated');

        // تحديث المتغير العام
        window.fileCreatorActive = true;

        // إظهار تأكيد مرئي فوري
        this.showActivationConfirmation();

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '✅ تم تفعيل File Creator Mode - جاهز لإنشاء الملفات مع روابط حقيقية مثل ChatGPT');
        }

        if (typeof speakText === 'function') {
            speakText('تم تفعيل File Creator Mode المتقدم. يمكنني الآن إنشاء أي نوع من الملفات مع التكامل الكامل مع الإنترنت. ما الملف الذي تريد إنشاءه؟');
        }
    }

    // Handle voice commands for file creation
    async handleVoiceCommand(command) {
        const lowerCommand = command.toLowerCase();

        // إعدادات طريقة العرض (أوامر صوتية)
        if (lowerCommand.includes('غير طريقة العرض') || lowerCommand.includes('إعدادات الملفات') ||
            lowerCommand.includes('اعدادات العرض') || lowerCommand.includes('طريقة عرض الملفات')) {
            this.showDisplayModeSettings();
            return '⚙️ تم فتح إعدادات طريقة عرض الملفات. يمكنك الاختيار بين: المحادثة، نافذة منبثقة، أو الاثنين معاً.';
        }

        if (lowerCommand.includes('عرض في المحادثة') || lowerCommand.includes('مثل شات جي بي تي') ||
            lowerCommand.includes('في الدردشة') || lowerCommand.includes('chat mode')) {
            this.setDisplayMode('chat');
            return '💬 تم تغيير طريقة العرض إلى: في المحادثة مثل ChatGPT. الملفات ستظهر كروابط تحميل في المحادثة.';
        }

        if (lowerCommand.includes('عرض نافذة منبثقة') || lowerCommand.includes('نافذة منبثقة') ||
            lowerCommand.includes('popup') || lowerCommand.includes('نافذة')) {
            this.setDisplayMode('popup');
            return '🪟 تم تغيير طريقة العرض إلى: نافذة منبثقة. ستظهر نافذة تأكيد عند إنشاء الملفات.';
        }

        if (lowerCommand.includes('عرض الاثنين') || lowerCommand.includes('الطريقتين') ||
            lowerCommand.includes('both') || lowerCommand.includes('معا')) {
            this.setDisplayMode('both');
            return '🔄 تم تغيير طريقة العرض إلى: الاثنين معاً. ستحصل على نافذة منبثقة ورابط في المحادثة.';
        }

        // PDF creation commands
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) {
            return await this.handlePDFCreation(command);
        }

        // PowerPoint creation commands
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) {
            return await this.handlePowerPointCreation(command);
        }

        // EXE creation commands
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج') || lowerCommand.includes('تطبيق')) {
            return await this.handleEXECreation(command);
        }

        // General file creation
        if (lowerCommand.includes('أنشئ') || lowerCommand.includes('اعمل') || lowerCommand.includes('create')) {
            return await this.handleGeneralFileCreation(command);
        }

        // Internet integration commands
        if (lowerCommand.includes('من الإنترنت') || lowerCommand.includes('ابحث') || lowerCommand.includes('حمل')) {
            return await this.handleInternetIntegration(command);
        }

        return await this.getAIFileCreationResponse(command);
    }

    // معالجة طلبات المستخدم (للتكامل مع المحادثة)
    async processUserRequest(userMessage) {
        console.log('📁 معالجة طلب المستخدم:', userMessage.substring(0, 50));

        try {
            // التحقق من التفعيل باستخدام المتغير العام الموثوق
            const isActive = window.fileCreatorActive === true || this.isActive === true;

            if (!isActive) {
                console.log('⚠️ File Creator غير مفعل');
                console.log('- this.isActive:', this.isActive);
                console.log('- window.fileCreatorActive:', window.fileCreatorActive);
                return 'يرجى تفعيل File Creator Mode أولاً من الأزرار العلوية';
            }

            console.log('✅ File Creator مفعل - بدء معالجة الطلب');

            // تحليل نوع الملف المطلوب
            const lowerMessage = userMessage.toLowerCase();

            if (lowerMessage.includes('pdf') || lowerMessage.includes('تقرير')) {
                return await this.handlePDFCreation(userMessage);
            } else if (lowerMessage.includes('powerpoint') || lowerMessage.includes('عرض') || lowerMessage.includes('ppt')) {
                return await this.handlePowerPointCreation(userMessage);
            } else if (lowerMessage.includes('exe') || lowerMessage.includes('برنامج') || lowerMessage.includes('تطبيق')) {
                return await this.handleEXECreation(userMessage);
            } else {
                // ملف عام - تحديد النوع تلقائياً
                return await this.handleGeneralFileCreation(userMessage);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة طلب المستخدم:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // معالجة إنشاء الملفات العامة
    async handleGeneralFileCreation(userMessage) {
        console.log('📄 معالجة إنشاء ملف عام:', userMessage.substring(0, 50));

        try {
            // استخدام detectFileType المحسن
            const detectedType = this.detectFileType(userMessage);

            // تحويل نوع الملف إلى معلومات التحميل
            const fileInfo = this.getFileTypeInfo(detectedType);
            const fileType = fileInfo.type;
            const mimeType = fileInfo.mimeType;
            const extension = fileInfo.extension;

            // استخراج الموضوع
            const topic = this.extractTopic(userMessage);
            const filename = `${topic}${extension}`;

            // إنشاء المحتوى
            const content = await this.generateFileContent(userMessage, fileType);

            if (content) {
                // إنشاء الملف
                const success = this.createDownloadableFile(content, filename, mimeType);

                if (success) {
                    return `✅ **تم إنشاء الملف بنجاح!**

📄 **الملف:** ${filename}
📊 **النوع:** ${fileType.toUpperCase()}
📝 **المحتوى:** ${content.length} حرف
🎯 **الحالة:** جاهز للتحميل

تم إنشاء الملف وهو متاح للتحميل الآن! 🎉`;
                } else {
                    return '❌ فشل في إنشاء الملف. يرجى المحاولة مرة أخرى.';
                }
            } else {
                return '❌ لم يتم توليد محتوى للملف. تأكد من تشغيل النموذج المحلي.';
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف العام:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // توليد محتوى الملف
    async generateFileContent(userMessage, fileType) {
        const prompt = `أنت خبير في إنشاء الملفات. المستخدم يطلب: "${userMessage}"

نوع الملف المطلوب: ${fileType}

قم بإنشاء محتوى احترافي ومفصل للملف يتضمن:
- محتوى عالي الجودة ومناسب لنوع الملف
- تنسيق صحيح وواضح
- معلومات دقيقة ومفيدة
- كود نظيف ومنظم (إذا كان ملف برمجي)

قدم المحتوى جاهز للاستخدام مباشرة.`;

        try {
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتوليد المحتوى...');
                const response = await window.openRouterIntegration.smartSendMessage(prompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 2000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتوليد المحتوى...');
                content = await technicalAssistant.getResponse(prompt);
            }

            // ثالثاً: محتوى افتراضي
            if (!content) {
                content = this.generateDefaultContent(userMessage, fileType);
            }

            return content;

        } catch (error) {
            console.error('❌ خطأ في توليد المحتوى:', error);
            return this.generateDefaultContent(userMessage, fileType);
        }
    }

    // توليد محتوى افتراضي
    generateDefaultContent(userMessage, fileType) {
        const topic = this.extractTopic(userMessage);

        switch (fileType) {
            case 'html':
                return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>${topic}</h1>
    <div class="content">
        <p>هذا محتوى تم إنشاؤه تلقائياً حول موضوع: ${topic}</p>
        <p>يمكنك تعديل هذا المحتوى حسب احتياجاتك.</p>
    </div>
</body>
</html>`;

            case 'javascript':
                return `// ${topic} - JavaScript File
// تم إنشاؤه بواسطة المساعد التقني الذكي

console.log('مرحباً من ${topic}');

// الوظائف الأساسية
function main() {
    console.log('تم تشغيل ${topic} بنجاح');
}

// تشغيل البرنامج
main();`;

            case 'python':
                return `# ${topic} - Python Script
# تم إنشاؤه بواسطة المساعد التقني الذكي

def main():
    """الوظيفة الرئيسية"""
    print("مرحباً من ${topic}")
    print("تم تشغيل البرنامج بنجاح")

if __name__ == "__main__":
    main()`;

            case 'json':
                return JSON.stringify({
                    "name": topic,
                    "description": `ملف JSON تم إنشاؤه حول ${topic}`,
                    "created": new Date().toISOString(),
                    "data": {
                        "example": "يمكنك إضافة بياناتك هنا"
                    }
                }, null, 2);

            default:
                return `${topic}

تم إنشاء هذا الملف بواسطة المساعد التقني الذكي.

الموضوع: ${topic}
التاريخ: ${new Date().toLocaleDateString('ar-SA')}

يمكنك تعديل هذا المحتوى حسب احتياجاتك.`;
        }
    }

    // إظهار تأكيد التفعيل
    showActivationConfirmation() {
        // إنشاء إشعار مرئي
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white; padding: 15px 25px; border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            font-weight: bold; font-size: 16px;
            animation: slideIn 0.5s ease-out;
        `;
        notification.innerHTML = '📁 تم تفعيل File Creator Mode بنجاح!';

        // إضافة CSS للحركة
        if (!document.getElementById('fileCreatorStyles')) {
            const style = document.createElement('style');
            style.id = 'fileCreatorStyles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Handle PDF creation with AI
    async handlePDFCreation(command) {
        const topic = this.extractTopic(command);
        
        const pdfPrompt = `أنت خبير في إنشاء المستندات الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء محتوى PDF احترافي شامل يتضمن:

1. **العنوان الرئيسي والفهرس**
2. **مقدمة شاملة**
3. **المحتوى الأساسي** (مقسم لأقسام منطقية)
4. **الصور والرسوم البيانية المطلوبة** (اذكر أنواعها)
5. **الخلاصة والتوصيات**
6. **المراجع والمصادر**

اجعل المحتوى:
- احترافي ومفصل
- منظم ومنسق
- يحتوي على معلومات حديثة ودقيقة
- مناسب للطباعة والعرض

قدم المحتوى بتنسيق جاهز للتحويل إلى PDF.`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PDF...');
                const response = await window.openRouterIntegration.smartSendMessage(pdfPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PDF...');
                const response = await window.huggingFaceManager.sendMessage(pdfPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PDF...');
                content = await technicalAssistant.getResponse(pdfPrompt);
            }

            if (content) {
                // Create PDF file
                await this.createPDFFile(topic, content);

                return `✅ **تم إنشاء ملف PDF بنجاح!**

📄 **الملف:** ${topic}.pdf
📊 **المحتوى:** ${content.length} حرف من المحتوى الاحترافي
🎨 **التنسيق:** تنسيق احترافي مع صور ورسوم بيانية

تم حفظ الملف وهو جاهز للتحميل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء محتوى احترافي.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PDF: ${error.message}`;
        }
    }

    // Handle PowerPoint creation with AI
    async handlePowerPointCreation(command) {
        const topic = this.extractTopic(command);
        
        const pptPrompt = `أنت خبير في إنشاء العروض التقديمية الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء عرض PowerPoint احترافي يتضمن:

1. **شريحة العنوان** - عنوان جذاب ومعلومات المقدم
2. **شريحة الفهرس** - نظرة عامة على المحتوى
3. **شرائح المحتوى الأساسي** (8-12 شريحة)
4. **شرائح الرسوم البيانية والإحصائيات**
5. **شريحة الخلاصة والتوصيات**
6. **شريحة الأسئلة والمناقشة**

لكل شريحة قدم:
- العنوان الرئيسي
- النقاط الأساسية (3-5 نقاط)
- اقتراحات للصور والرسوم
- ملاحظات للمقدم

اجعل العرض:
- جذاب ومتفاعل
- مناسب للجمهور المستهدف
- يحتوي على معلومات قيمة ومحدثة
- سهل الفهم والمتابعة`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PowerPoint...');
                const response = await window.openRouterIntegration.smartSendMessage(pptPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PowerPoint...');
                const response = await window.huggingFaceManager.sendMessage(pptPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PowerPoint...');
                content = await technicalAssistant.getResponse(pptPrompt);
            }

            if (content) {
                // Create PowerPoint file
                await this.createPowerPointFile(topic, content);

                return `✅ **تم إنشاء عرض PowerPoint بنجاح!**

🎯 **الملف:** ${topic}.pptx
📊 **المحتوى:** عرض احترافي متكامل
🎨 **التصميم:** تصميم حديث وجذاب
📸 **الصور:** صور ورسوم بيانية مناسبة

تم حفظ العرض وهو جاهز للاستخدام! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء عروض احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PowerPoint: ${error.message}`;
        }
    }

    // Handle EXE creation with AI
    async handleEXECreation(command) {
        const programType = this.extractProgramType(command);
        
        const exePrompt = `أنت خبير في تطوير البرامج والتطبيقات. المستخدم يطلب: "${command}"

نوع البرنامج: ${programType}

قم بإنشاء برنامج EXE احترافي يتضمن:

1. **الكود المصدري الكامل** (Python/C++/C#)
2. **واجهة المستخدم الرسومية** (GUI)
3. **الوظائف الأساسية والمتقدمة**
4. **معالجة الأخطاء والاستثناءات**
5. **ملف التعليمات والمساعدة**
6. **أيقونة البرنامج والموارد**

اجعل البرنامج:
- سهل الاستخدام ومفهوم
- يحتوي على جميع الوظائف المطلوبة
- محمي من الأخطاء
- قابل للتوزيع والتشغيل

قدم الكود الكامل مع تعليمات التجميع.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const code = await technicalAssistant.getResponse(exePrompt);
                
                // Create EXE file
                await this.createEXEFile(programType, code);
                
                return `✅ **تم إنشاء برنامج EXE بنجاح!**

💻 **الملف:** ${programType}.exe
🔧 **الوظائف:** جميع الوظائف المطلوبة
🎨 **الواجهة:** واجهة رسومية احترافية
📁 **الحجم:** محسن للأداء

تم إنشاء البرنامج وهو جاهز للتشغيل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء برامج احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء EXE: ${error.message}`;
        }
    }

    // Extract topic from command
    extractTopic(command) {
        // Remove common words and extract main topic
        const cleanCommand = command
            .replace(/أنشئ|اعمل|pdf|powerpoint|عرض|تقرير|عن|حول|في/gi, '')
            .trim();
        
        return cleanCommand || 'موضوع عام';
    }

    // Extract program type from command
    extractProgramType(command) {
        if (command.includes('حاسبة') || command.includes('calculator')) return 'آلة حاسبة';
        if (command.includes('محول') || command.includes('converter')) return 'محول ملفات';
        if (command.includes('منظم') || command.includes('organizer')) return 'منظم ملفات';
        if (command.includes('لعبة') || command.includes('game')) return 'لعبة بسيطة';
        
        return 'أداة مساعدة';
    }

    // Create PDF file with professional formatting and real download
    async createPDFFile(topic, content) {
        try {
            console.log('📄 إنشاء ملف PDF احترافي...');

            // إنشاء محتوى PDF كـ HTML منسق للطباعة
            const pdfContent = this.generateProfessionalPDFContent(topic, content);

            // إنشاء ملف HTML قابل للتحويل إلى PDF
            const htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6; color: #333; background: white;
            margin: 0; padding: 20px;
        }
        .header { text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .title { font-size: 28px; font-weight: bold; color: #007bff; margin-bottom: 10px; }
        .subtitle { font-size: 16px; color: #666; }
        .content { font-size: 14px; text-align: justify; }
        .section { margin: 20px 0; }
        .section h2 { color: #007bff; border-bottom: 2px solid #eee; padding-bottom: 5px; }
        .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 20px; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    ${pdfContent}
</body>
</html>`;

            // إنشاء الملف وتحميله
            const success = this.createDownloadableFile(htmlContent, `${topic}.html`, 'text/html');

            if (success) {
                // إنشاء نسخة نصية أيضاً
                const textContent = this.generateTextVersion(topic, content);
                this.createDownloadableFile(textContent, `${topic}.txt`, 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'PDF',
                    name: `${topic}.html`,
                    created: new Date(),
                    size: content.length
                });

                console.log('✅ تم إنشاء ملف PDF بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('PDF', `${topic}.html`, htmlContent.length);
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF:', error);
            throw error;
        }
    }

    // Create PowerPoint file with real download
    async createPowerPointFile(topic, content) {
        try {
            console.log('🎯 إنشاء عرض PowerPoint احترافي...');

            // Create PowerPoint-like HTML presentation
            const slides = this.parseContentToSlides(content);
            const pptHTML = this.generatePowerPointHTML(topic, slides);

            // إنشاء الملف وتحميله
            const success = this.createDownloadableFile(pptHTML, `${topic}_presentation.html`, 'text/html');

            if (success) {
                // إنشاء ملف نصي للمحتوى أيضاً
                const textContent = this.generatePresentationText(topic, slides);
                this.createDownloadableFile(textContent, `${topic}_notes.txt`, 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'PowerPoint',
                    name: `${topic}_presentation.html`,
                    created: new Date(),
                    slides: slides.length
                });

                console.log('✅ تم إنشاء عرض PowerPoint بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('PowerPoint', `${topic}_presentation.html`, slides.length);
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء PowerPoint:', error);
            throw error;
        }
    }

    // Create EXE file (Python script with compilation instructions)
    async createEXEFile(programType, code) {
        try {
            console.log('💻 إنشاء برنامج EXE...');

            // Create Python script
            const pythonCode = this.generatePythonCode(programType, code);

            // إنشاء ملف Python وتحميله
            const pythonSuccess = this.createDownloadableFile(pythonCode, `${programType}.py`, 'text/x-python');

            if (pythonSuccess) {
                // Create batch file for compilation
                const batchScript = this.generateCompilationScript(programType);
                this.createDownloadableFile(batchScript, 'compile_to_exe.bat', 'text/plain');

                // Create README file
                const readmeContent = this.generateReadmeFile(programType);
                this.createDownloadableFile(readmeContent, 'README.txt', 'text/plain');

                // Create requirements file
                const requirements = this.generateRequirementsFile(programType);
                this.createDownloadableFile(requirements, 'requirements.txt', 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'EXE Package',
                    name: `${programType}.py`,
                    created: new Date(),
                    files: 4
                });

                console.log('✅ تم إنشاء حزمة EXE بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('EXE Package', `${programType}.py`, '4 ملفات');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء EXE:', error);
            throw error;
        }
    }

    // Load jsPDF library dynamically
    async loadJsPDF() {
        return new Promise((resolve, reject) => {
            if (window.jspdf) {
                resolve(window.jspdf);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => resolve(window.jspdf);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Parse content to slides
    parseContentToSlides(content) {
        const sections = content.split(/\n\s*\n/);
        return sections.map((section, index) => ({
            title: `شريحة ${index + 1}`,
            content: section.trim()
        }));
    }

    // Generate PowerPoint HTML
    generatePowerPointHTML(topic, slides) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - عرض تقديمي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f0f0f0; }
        .presentation { max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .slide { padding: 60px; min-height: 500px; border-bottom: 2px solid #eee; page-break-after: always; }
        .slide h1 { color: #2c3e50; font-size: 2.5rem; margin-bottom: 30px; text-align: center; }
        .slide h2 { color: #34495e; font-size: 2rem; margin-bottom: 20px; }
        .slide p { font-size: 1.2rem; line-height: 1.6; margin-bottom: 15px; }
        .slide ul { font-size: 1.1rem; line-height: 1.8; }
        .title-slide { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; }
        .title-slide h1 { color: white; font-size: 3rem; }
        @media print { .slide { page-break-after: always; } }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide title-slide">
            <h1>${topic}</h1>
            <p style="font-size: 1.5rem; margin-top: 50px;">عرض تقديمي احترافي</p>
            <p style="font-size: 1.2rem;">تم إنشاؤه بواسطة المساعد التقني الذكي</p>
            <p style="font-size: 1rem; margin-top: 100px;">${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        ${slides.map(slide => `
        <div class="slide">
            <h2>${slide.title}</h2>
            <div>${slide.content.replace(/\n/g, '<br>')}</div>
        </div>
        `).join('')}
    </div>
</body>
</html>`;
    }

    // Generate Python code for EXE
    generatePythonCode(programType, aiCode) {
        const baseCode = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${programType}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

class ${programType.replace(/\s+/g, '')}App:
    def __init__(self, root):
        self.root = root
        self.root.title("${programType}")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # Set up the GUI
        self.setup_gui()

    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="${programType}", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Add AI-generated functionality here
        ${this.extractPythonFunctionality(aiCode)}

    def show_about(self):
        messagebox.showinfo("حول البرنامج",
                          "${programType}\\nتم إنشاؤه بواسطة المساعد التقني الذكي")

def main():
    root = tk.Tk()
    app = ${programType.replace(/\s+/g, '')}App(root)
    root.mainloop()

if __name__ == "__main__":
    main()
`;
        return baseCode;
    }

    // Extract Python functionality from AI code
    extractPythonFunctionality(aiCode) {
        // Simple extraction - in real implementation, this would be more sophisticated
        return `        # الوظائف الأساسية
        self.create_main_interface()

    def create_main_interface(self):
        # واجهة البرنامج الرئيسية
        pass`;
    }

    // Generate compilation script
    generateCompilationScript(programType) {
        return `@echo off
echo تجميع ${programType} إلى ملف EXE...
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install pyinstaller

REM تجميع البرنامج
echo تجميع البرنامج...
pyinstaller --onefile --windowed "${programType}.py"

echo.
echo تم تجميع البرنامج بنجاح!
echo ستجد ملف EXE في مجلد dist
pause`;
    }

    // Generate README file
    generateReadmeFile(programType) {
        return `${programType} - دليل الاستخدام

تم إنشاء هذا البرنامج بواسطة المساعد التقني الذكي

محتويات الحزمة:
- ${programType}.py: الكود المصدري للبرنامج
- compile.bat: ملف تجميع البرنامج إلى EXE
- README.txt: هذا الملف

طريقة التشغيل:
1. تأكد من تثبيت Python على النظام
2. شغل ملف compile.bat لتجميع البرنامج
3. ستجد ملف EXE في مجلد dist

أو يمكنك تشغيل البرنامج مباشرة:
python "${programType}.py"

للدعم والمساعدة:
استخدم المساعد التقني الذكي`;
    }

    // Create ZIP package
    async createZipPackage(name, files) {
        // Using JSZip library for creating ZIP files
        const JSZip = window.JSZip || await this.loadJSZip();
        const zip = new JSZip();

        // Add files to ZIP
        Object.entries(files).forEach(([filename, content]) => {
            zip.file(filename, content);
        });

        // Generate ZIP file
        const content = await zip.generateAsync({ type: 'blob' });

        // Download ZIP file
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}_package.zip`;
        a.click();

        URL.revokeObjectURL(url);
    }

    // Load JSZip library
    async loadJSZip() {
        return new Promise((resolve, reject) => {
            if (window.JSZip) {
                resolve(window.JSZip);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = () => resolve(window.JSZip);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Handle general file creation
    async handleGeneralFileCreation(command) {
        const fileType = this.detectFileType(command);

        switch (fileType) {
            case 'pdf':
                return await this.handlePDFCreation(command);
            case 'powerpoint':
                return await this.handlePowerPointCreation(command);
            case 'exe':
                return await this.handleEXECreation(command);
            case 'word':
                return await this.handleWordCreation(command);
            case 'excel':
                return await this.handleExcelCreation(command);
            case 'html':
                return await this.handleHTMLCreation(command);
            case 'css':
                return await this.handleCSSCreation(command);
            case 'javascript':
                return await this.handleJavaScriptCreation(command);
            case 'python':
                return await this.handlePythonCreation(command);
            case 'java':
                return await this.handleJavaCreation(command);
            case 'cpp':
                return await this.handleCppCreation(command);
            case 'json':
                return await this.handleJSONCreation(command);
            case 'xml':
                return await this.handleXMLCreation(command);
            case 'markdown':
                return await this.handleMarkdownCreation(command);
            case 'text':
                return await this.handleTextCreation(command);
            default:
                return await this.handleHTMLCreation(command); // افتراضي HTML
        }
    }

    // Detect file type from command
    detectFileType(command) {
        const lowerCommand = command.toLowerCase();

        // ملفات المكتب
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) return 'pdf';
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) return 'powerpoint';
        if (lowerCommand.includes('word') || lowerCommand.includes('مستند')) return 'word';
        if (lowerCommand.includes('excel') || lowerCommand.includes('جدول')) return 'excel';

        // ملفات البرمجة والويب
        if (lowerCommand.includes('html') || lowerCommand.includes('صفحة') || lowerCommand.includes('موقع')) return 'html';
        if (lowerCommand.includes('css') || lowerCommand.includes('تنسيق') || lowerCommand.includes('ستايل')) return 'css';
        if (lowerCommand.includes('javascript') || lowerCommand.includes('js') || lowerCommand.includes('جافا سكريبت')) return 'javascript';
        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون') || lowerCommand.includes('.py')) return 'python';
        if (lowerCommand.includes('java') || lowerCommand.includes('جافا') || lowerCommand.includes('.java')) return 'java';
        if (lowerCommand.includes('cpp') || lowerCommand.includes('c++') || lowerCommand.includes('سي بلس')) return 'cpp';
        if (lowerCommand.includes('json') || lowerCommand.includes('بيانات') || lowerCommand.includes('api')) return 'json';
        if (lowerCommand.includes('xml') || lowerCommand.includes('markup')) return 'xml';
        if (lowerCommand.includes('markdown') || lowerCommand.includes('md') || lowerCommand.includes('توثيق')) return 'markdown';

        // ملفات أخرى
        if (lowerCommand.includes('txt') || lowerCommand.includes('نص') || lowerCommand.includes('text')) return 'text';
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج')) return 'exe';

        return 'html'; // افتراضي HTML بدلاً من general
    }

    // تحويل نوع الملف إلى معلومات التحميل
    getFileTypeInfo(detectedType) {
        const typeMap = {
            'html': { type: 'html', mimeType: 'text/html', extension: '.html' },
            'css': { type: 'css', mimeType: 'text/css', extension: '.css' },
            'javascript': { type: 'javascript', mimeType: 'text/javascript', extension: '.js' },
            'python': { type: 'python', mimeType: 'text/x-python', extension: '.py' },
            'java': { type: 'java', mimeType: 'text/x-java', extension: '.java' },
            'cpp': { type: 'cpp', mimeType: 'text/x-c++src', extension: '.cpp' },
            'json': { type: 'json', mimeType: 'application/json', extension: '.json' },
            'xml': { type: 'xml', mimeType: 'application/xml', extension: '.xml' },
            'markdown': { type: 'markdown', mimeType: 'text/markdown', extension: '.md' },
            'text': { type: 'text', mimeType: 'text/plain', extension: '.txt' },
            'pdf': { type: 'pdf', mimeType: 'application/pdf', extension: '.pdf' },
            'powerpoint': { type: 'powerpoint', mimeType: 'text/html', extension: '.html' },
            'word': { type: 'word', mimeType: 'text/html', extension: '.html' },
            'excel': { type: 'excel', mimeType: 'text/csv', extension: '.csv' },
            'exe': { type: 'exe', mimeType: 'text/x-python', extension: '.py' }
        };

        return typeMap[detectedType] || typeMap['html'];
    }

    // Handle internet integration
    async handleInternetIntegration(command) {
        if (!this.internetAccess) {
            return 'التكامل مع الإنترنت غير مفعل حالياً';
        }

        const internetIntegration = new InternetIntegration();

        if (command.includes('صور') || command.includes('images')) {
            const topic = this.extractTopic(command);
            const images = await internetIntegration.searchImages(topic);
            return `تم العثور على ${images.length} صورة عن "${topic}". سيتم استخدامها في الملف.`;
        }

        if (command.includes('بيانات') || command.includes('معلومات')) {
            const topic = this.extractTopic(command);
            const data = await internetIntegration.fetchRealTimeData(topic);
            return `تم جلب معلومات حديثة عن "${topic}" من الإنترنت.`;
        }

        return 'تم تفعيل التكامل مع الإنترنت لجلب المحتوى المطلوب.';
    }

    // تحديد نوع الملف الذكي من السياق
    detectSmartFileType(command) {
        const lowerCommand = command.toLowerCase();

        // فحص السياق لتحديد النوع الأنسب
        if (lowerCommand.includes('صفحة') || lowerCommand.includes('موقع') || lowerCommand.includes('تسجيل دخول') || lowerCommand.includes('نموذج')) {
            return 'صفحة HTML';
        }
        if (lowerCommand.includes('تقرير') || lowerCommand.includes('مستند') || lowerCommand.includes('وثيقة')) {
            return 'مستند HTML';
        }
        if (lowerCommand.includes('قائمة') || lowerCommand.includes('جدول') || lowerCommand.includes('بيانات')) {
            return 'جدول HTML';
        }
        if (lowerCommand.includes('سكريبت') || lowerCommand.includes('برنامج') || lowerCommand.includes('كود')) {
            return 'ملف برمجي';
        }

        return 'ملف HTML'; // افتراضي
    }

    // إنشاء ملف ذكي حسب النوع
    async createSmartFile(topic, content, fileType, originalCommand) {
        console.log(`📁 إنشاء ${fileType} احترافي:`, topic);

        let finalContent, filename, mimeType;

        if (fileType.includes('HTML') || fileType.includes('صفحة') || fileType.includes('مستند') || fileType.includes('جدول')) {
            // إنشاء HTML احترافي
            finalContent = this.generateProfessionalHTML(topic, content, fileType);
            filename = `${topic.replace(/\s+/g, '_')}.html`;
            mimeType = 'text/html';
        } else if (fileType.includes('برمجي')) {
            // إنشاء ملف برمجي
            finalContent = this.generateCodeFile(topic, content, originalCommand);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        } else {
            // افتراضي - ملف نصي منسق
            finalContent = this.generateFormattedText(topic, content);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        }

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(finalContent, filename, mimeType);

        if (success) {
            this.creationHistory.push({
                type: fileType,
                name: filename,
                created: new Date(),
                size: finalContent.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess(fileType, filename, finalContent.length + ' حرف');
        }
    }

    // إنشاء HTML احترافي
    generateProfessionalHTML(topic, content, fileType) {
        let pageTitle = topic;
        let pageStyle = '';

        if (fileType.includes('تسجيل دخول') || content.includes('تسجيل دخول')) {
            pageStyle = this.getLoginPageStyle();
        } else if (fileType.includes('جدول')) {
            pageStyle = this.getTablePageStyle();
        } else {
            pageStyle = this.getDefaultPageStyle();
        }

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        ${pageStyle}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>${pageTitle}</h1>
        </header>
        <main class="content">
            ${this.formatContentForHTML(content)}
        </main>
        <footer>
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </footer>
    </div>
</body>
</html>`;
    }

    // Get AI response for file creation - إنشاء ملف فعلي مباشرة
    async getAIFileCreationResponse(command) {
        const topic = this.extractTopic(command);

        // تحديد نوع الملف الذكي من السياق
        const smartFileType = this.detectSmartFileType(command);

        const prompt = `أنت خبير في إنشاء الملفات الاحترافية. المستخدم يطلب: "${command}"

أنشئ محتوى ${smartFileType} احترافي ومفصل عن: ${topic}

يجب أن يكون المحتوى:
1. احترافي ومنظم
2. مفصل وشامل
3. جاهز للاستخدام مباشرة
4. مناسب لنوع الملف المطلوب

أنشئ المحتوى الكامل الآن بدون اقتراحات.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(prompt);

                // إنشاء الملف مباشرة حسب النوع المحدد
                await this.createSmartFile(topic, content, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح!`;
            } else {
                // إنشاء محتوى افتراضي إذا لم يكن النموذج متاح
                const defaultContent = this.generateDefaultContent(topic, smartFileType);
                await this.createSmartFile(topic, defaultContent, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح! (محتوى افتراضي)`;
            }
        } catch (error) {
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // Handle Word document creation
    async handleWordCreation(command) {
        const topic = this.extractTopic(command);

        const wordPrompt = `أنت خبير في إنشاء المستندات الاحترافية. أنشئ مستند Word احترافي عن: ${topic}

يجب أن يتضمن المستند:
1. عنوان رئيسي جذاب
2. فهرس المحتويات
3. مقدمة شاملة
4. المحتوى الأساسي (مقسم لأقسام)
5. خلاصة وتوصيات
6. مراجع ومصادر

اجعل المحتوى احترافي ومفصل ومناسب للطباعة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(wordPrompt);
                await this.createWordFile(topic, content);
                return `✅ تم إنشاء مستند Word "${topic}.docx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء مستندات Word احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء مستند Word: ${error.message}`;
        }
    }

    // Handle Excel creation
    async handleExcelCreation(command) {
        const topic = this.extractTopic(command);

        const excelPrompt = `أنت خبير في إنشاء جداول البيانات. أنشئ جدول Excel احترافي عن: ${topic}

يجب أن يتضمن:
1. أوراق عمل متعددة
2. بيانات منظمة ومفيدة
3. رسوم بيانية
4. معادلات وحسابات
5. تنسيق احترافي

قدم هيكل الجدول والبيانات المطلوبة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(excelPrompt);
                await this.createExcelFile(topic, content);
                return `✅ تم إنشاء جدول Excel "${topic}.xlsx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء جداول Excel احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء جدول Excel: ${error.message}`;
        }
    }

    // Handle HTML creation
    async handleHTMLCreation(command) {
        const topic = this.extractTopic(command);

        const htmlPrompt = `أنت خبير في تطوير الويب. أنشئ صفحة HTML احترافية عن: ${topic}

يجب أن تتضمن:
1. هيكل HTML5 صحيح
2. تصميم CSS مدمج جميل
3. محتوى تفاعلي
4. تصميم متجاوب
5. أفضل الممارسات

أنشئ كود HTML كامل وجاهز للاستخدام.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(htmlPrompt);
                await this.createHTMLFile(topic, content);
                return `✅ تم إنشاء صفحة HTML "${topic}.html" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء صفحات HTML احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء صفحة HTML: ${error.message}`;
        }
    }

    // Create Word file with real download
    async createWordFile(topic, content) {
        console.log('📄 إنشاء مستند Word احترافي...');

        // Create HTML version of Word document
        const wordHTML = this.generateWordHTML(topic, content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(wordHTML, `${topic}_document.html`, 'text/html');

        if (success) {
            // إنشاء نسخة نصية أيضاً
            const textContent = this.generateTextVersion(topic, content);
            this.createDownloadableFile(textContent, `${topic}_document.txt`, 'text/plain');

            this.creationHistory.push({
                type: 'Word Document',
                name: `${topic}_document.html`,
                created: new Date(),
                size: content.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('Word Document', `${topic}_document.html`, content.length);
        }
    }

    // Create Excel file with real download
    async createExcelFile(topic, content) {
        console.log('📊 إنشاء جدول Excel احترافي...');

        // Create CSV version of Excel data
        const csvData = this.generateCSVData(topic, content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(csvData, `${topic}_spreadsheet.csv`, 'text/csv');

        if (success) {
            // إنشاء ملف HTML للعرض أيضاً
            const htmlTable = this.generateExcelHTML(topic, csvData);
            this.createDownloadableFile(htmlTable, `${topic}_table.html`, 'text/html');

            this.creationHistory.push({
                type: 'Excel Spreadsheet',
                name: `${topic}_spreadsheet.csv`,
                created: new Date(),
                rows: csvData.split('\n').length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('Excel Spreadsheet', `${topic}_spreadsheet.csv`, csvData.split('\n').length + ' صف');
        }
    }

    // Create HTML file with real download
    async createHTMLFile(topic, content) {
        console.log('🌐 إنشاء صفحة HTML احترافية...');

        // تنظيف وتحسين كود HTML
        const cleanHTML = this.cleanAndFormatHTML(content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(cleanHTML, `${topic}.html`, 'text/html');

        if (success) {
            this.creationHistory.push({
                type: 'HTML Page',
                name: `${topic}.html`,
                created: new Date(),
                size: cleanHTML.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('HTML Page', `${topic}.html`, cleanHTML.length + ' حرف');
        }
    }

    // تنظيف وتحسين كود HTML
    cleanAndFormatHTML(content) {
        // إذا كان المحتوى يحتوي على HTML كامل، استخدمه كما هو
        if (content.includes('<!DOCTYPE') || content.includes('<html')) {
            return content;
        }

        // إذا لم يكن كذلك، أنشئ هيكل HTML كامل
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة احترافية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .content {
            font-size: 16px;
            color: #333;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;
    }

    // Generate Word HTML
    generateWordHTML(topic, content) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        p { text-align: justify; margin-bottom: 15px; }
        .header { text-align: center; margin-bottom: 40px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #7f8c8d; }
        @media print { body { margin: 0; padding: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>مستند احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي - File Creator Mode</p>
    </div>
</body>
</html>`;
    }

    // Generate CSV data
    generateCSVData(topic, content) {
        const timestamp = new Date().toLocaleDateString('ar-SA');

        // تحليل المحتوى لإنشاء بيانات مفيدة
        const lines = content.split('\n').filter(line => line.trim());
        let csvData = `العنوان,القيمة,التاريخ,الملاحظات\n`;

        // إضافة بيانات أساسية
        csvData += `${topic},البيانات الرئيسية,${timestamp},تم إنشاؤها بواسطة AI\n`;

        // إضافة بيانات من المحتوى
        lines.slice(0, 10).forEach((line, index) => {
            const cleanLine = line.replace(/[,]/g, '؛').substring(0, 50);
            csvData += `عنصر ${index + 1},${cleanLine},${timestamp},من المحتوى الأصلي\n`;
        });

        // إضافة إحصائيات
        csvData += `إجمالي الأسطر,${lines.length},${timestamp},إحصائية\n`;
        csvData += `عدد الكلمات,${content.split(' ').length},${timestamp},إحصائية\n`;
        csvData += `عدد الأحرف,${content.length},${timestamp},إحصائية\n`;

        return csvData;
    }

    // إنشاء جدول HTML من CSV
    generateExcelHTML(topic, csvData) {
        const lines = csvData.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',');
        const rows = lines.slice(1);

        let tableHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول: ${topic}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px; background: #f5f5f5;
        }
        .container {
            background: white; padding: 30px; border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50; text-align: center; margin-bottom: 30px;
            border-bottom: 3px solid #3498db; padding-bottom: 15px;
        }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; text-align: center;
            font-weight: bold; border: 1px solid #ddd;
        }
        td {
            padding: 12px; border: 1px solid #ddd; text-align: center;
            transition: background-color 0.3s ease;
        }
        tr:nth-child(even) { background-color: #f8f9fa; }
        tr:hover { background-color: #e3f2fd; }
        .footer {
            text-align: center; margin-top: 30px; color: #666;
            font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 ${topic}</h1>
        <table>
            <thead>
                <tr>`;

        // إضافة العناوين
        headers.forEach(header => {
            tableHTML += `<th>${header.trim()}</th>`;
        });

        tableHTML += `</tr>
            </thead>
            <tbody>`;

        // إضافة الصفوف
        rows.forEach(row => {
            if (row.trim()) {
                const cells = row.split(',');
                tableHTML += '<tr>';
                cells.forEach(cell => {
                    tableHTML += `<td>${cell.trim()}</td>`;
                });
                tableHTML += '</tr>';
            }
        });

        tableHTML += `</tbody>
        </table>
        <div class="footer">
            <p>تم إنشاء هذا الجدول بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;

        return tableHTML;
    }

    // تنسيق المحتوى لـ HTML
    formatContentForHTML(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء ملف برمجي
    generateCodeFile(topic, content, originalCommand) {
        const lowerCommand = originalCommand.toLowerCase();

        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

${content}

if __name__ == "__main__":
    print("${topic}")`;
        } else if (lowerCommand.includes('javascript') || lowerCommand.includes('js')) {
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

${content}

console.log("${topic}");`;
        } else {
            return `/*
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
*/

${content}`;
        }
    }

    // إنشاء نص منسق
    generateFormattedText(topic, content) {
        return `${topic}
${'='.repeat(topic.length)}

${content}

---
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // إنشاء محتوى افتراضي
    generateDefaultContent(topic, fileType) {
        return `هذا محتوى افتراضي عن ${topic}.

تم إنشاء هذا ${fileType} بواسطة المساعد التقني الذكي.

يمكنك تعديل هذا المحتوى حسب احتياجاتك.

المميزات:
• محتوى منظم ومرتب
• تصميم احترافي
• جاهز للاستخدام
• قابل للتخصيص

للمزيد من المعلومات، يرجى الرجوع إلى الوثائق الرسمية.`;
    }

    // أنماط CSS للصفحات المختلفة
    getDefaultPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .content {
            padding: 40px;
            font-size: 16px;
            color: #333;
        }
        footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }
        p {
            margin-bottom: 15px;
        }
        `;
    }

    getLoginPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        header h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        `;
    }

    getTablePageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        th {
            background: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        `;
    }

    // Deactivate File Creator Mode
    deactivate() {
        this.isActive = false;
        console.log('📁 File Creator Mode deactivated');

        // تحديث المتغير العام
        window.fileCreatorActive = false;

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '⏹️ تم إيقاف File Creator Mode');
        }

        if (typeof speakText === 'function') {
            speakText('تم إلغاء تفعيل File Creator Mode.');
        }
    }

    // Get creation history
    getCreationHistory() {
        return this.creationHistory;
    }

    // Clear creation history
    clearHistory() {
        this.creationHistory = [];
        console.log('🗑️ تم مسح سجل إنشاء الملفات');
    }

    // ===========================================
    // دوال التحميل الحقيقي للملفات (مثل ChatGPT)
    // ===========================================

    // إنشاء ملف قابل للتحميل مع رابط تفاعلي (مثل ChatGPT) - 100% محلي
    createDownloadableFile(content, filename, type = 'text/plain') {
        console.log('📁 إنشاء ملف قابل للتحميل محلياً:', filename);

        try {
            // إنشاء Blob من المحتوى (100% محلي - لا مواقع خارجية)
            const blob = new Blob([content], { type: type });
            const url = URL.createObjectURL(blob);

            console.log('✅ تم إنشاء Blob URL محلي:', url);
            console.log('📊 حجم الملف:', blob.size, 'بايت');
            console.log('📄 نوع الملف:', blob.type);

            // إضافة رابط تحميل تفاعلي في المحادثة (مثل ChatGPT) - بدون تحميل تلقائي
            this.addDownloadLinkToChat(filename, url, blob.size);

            console.log('✅ تم إنشاء رابط التحميل المحلي بنجاح:', filename);
            return true;

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            return false;
        }
    }

    // إضافة رابط تحميل تفاعلي للمحادثة (مثل ChatGPT)
    addDownloadLinkToChat(filename, downloadUrl, fileSize) {
        try {
            // البحث عن حاوي المحادثة بطرق متعددة
            let chatContainer = document.getElementById('chatContainer') ||
                               document.querySelector('.chat-container') ||
                               document.querySelector('#chat-messages') ||
                               document.querySelector('.messages-container') ||
                               document.querySelector('[class*="chat"]') ||
                               document.querySelector('[id*="chat"]');

            // إذا لم يتم العثور على حاوي، إنشاء حاوية مؤقتة
            if (!chatContainer) {
                console.log('📦 إنشاء حاوية رسائل مؤقتة...');
                chatContainer = this.createTemporaryChatContainer();
            }

            console.log('✅ استخدام حاوي المحادثة:', chatContainer.id || chatContainer.className);

            // إنشاء رسالة مع رابط التحميل
            const downloadMessage = document.createElement('div');
            downloadMessage.className = 'message assistant';
            downloadMessage.style.cssText = `
                margin: 10px 0;
                padding: 0;
                display: flex;
                align-items: flex-start;
                gap: 10px;
            `;

            // أيقونة المساعد
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
            avatar.style.cssText = `
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #4CAF50;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                flex-shrink: 0;
            `;

            // محتوى الرسالة مع رابط التحميل
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.style.cssText = `
                background: #f8f9fa;
                border-radius: 12px;
                padding: 15px;
                max-width: 70%;
                border-left: 4px solid #4CAF50;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;

            // تحديد أيقونة الملف حسب النوع
            const fileIcon = this.getFileIcon(filename);
            const fileExtension = filename.split('.').pop().toUpperCase();
            const fileSizeFormatted = this.formatFileSize(fileSize);

            messageContent.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <strong style="color: #2E7D32;">📁 تم إنشاء الملف بنجاح!</strong>
                </div>
                <div style="background: white; border-radius: 8px; padding: 12px; border: 1px solid #e0e0e0;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="font-size: 24px;">${fileIcon}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #333; font-size: 14px;">${filename}</div>
                            <div style="color: #666; font-size: 12px;">${fileExtension} • ${fileSizeFormatted}</div>
                        </div>
                    </div>
                    <a href="${downloadUrl}"
                       download="${filename}"
                       style="
                           display: inline-flex;
                           align-items: center;
                           justify-content: center;
                           gap: 8px;
                           background: #4CAF50;
                           color: white;
                           text-decoration: none;
                           padding: 12px 20px;
                           border-radius: 8px;
                           font-size: 14px;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           width: 100%;
                           box-sizing: border-box;
                           box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
                       "
                       onmouseover="this.style.background='#45a049'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.4)'"
                       onmouseout="this.style.background='#4CAF50'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.3)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 16px;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    💡 اضغط على الزر أعلاه لتحميل الملف مباشرة
                </div>
            `;

            downloadMessage.appendChild(avatar);
            downloadMessage.appendChild(messageContent);
            chatContainer.appendChild(downloadMessage);

            // التمرير لأسفل لإظهار الرسالة الجديدة
            chatContainer.scrollTop = chatContainer.scrollHeight;

            console.log('✅ تم إضافة رابط التحميل للمحادثة');

        } catch (error) {
            console.error('❌ خطأ في إضافة رابط التحميل:', error);
            // تحميل مباشر كبديل
            this.directDownload(downloadUrl, filename);
        }
    }

    // إضافة رابط تحميل عبر addMessage (مثل ChatGPT)
    addDownloadLinkViaAddMessage(filename, downloadUrl, fileSize) {
        const fileIcon = this.getFileIcon(filename);
        const fileExtension = filename.split('.').pop().toUpperCase();
        const fileSizeFormatted = this.formatFileSize(fileSize);

        // إنشاء HTML للرابط التفاعلي (مثل ChatGPT)
        const downloadHTML = `
            <div style="background: #f8f9fa; border-radius: 12px; padding: 15px; margin: 10px 0; border-left: 4px solid #4CAF50; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 12px;">
                    <strong style="color: #2E7D32; font-size: 16px;">${fileIcon} تم إنشاء الملف:</strong>
                </div>

                <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <span style="font-size: 28px;">${fileIcon}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">${filename}</div>
                            <div style="color: #666; font-size: 13px;">${fileExtension} • ${fileSizeFormatted} • تم إنشاؤه بالذكاء الاصطناعي</div>
                        </div>
                    </div>

                    <a href="${downloadUrl}"
                       download="${filename}"
                       style="
                           display: inline-flex;
                           align-items: center;
                           justify-content: center;
                           gap: 8px;
                           background: #4CAF50;
                           color: white;
                           text-decoration: none;
                           padding: 12px 20px;
                           border-radius: 8px;
                           font-size: 14px;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           width: 100%;
                           box-sizing: border-box;
                           box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
                       "
                       onmouseover="this.style.background='#45a049'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.4)'"
                       onmouseout="this.style.background='#4CAF50'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.3)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 16px;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>

                <div style="font-size: 12px; color: #666; text-align: center;">
                    💡 <strong>رابط محلي 100%</strong> - لا رفع خارجي • تحميل مباشر مثل ChatGPT
                </div>
            </div>
        `;

        // إضافة الرابط للمحادثة بطرق متعددة
        if (typeof addMessage === 'function') {
            addMessage('assistant', downloadHTML);
            console.log('✅ تم إضافة رابط التحميل المحلي عبر addMessage');
        } else if (chatContainer) {
            // إضافة مباشرة للحاوي
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = downloadHTML;
            chatContainer.appendChild(messageDiv);
            console.log('✅ تم إضافة رابط التحميل المحلي مباشرة للحاوي');
        } else {
            console.warn('⚠️ لا يمكن عرض الرابط، استخدام التحميل المباشر');
            this.directDownload(downloadUrl, filename);
        }

        // تأكيد أن الرابط محلي وليس خارجي
        if (downloadUrl.startsWith('blob:')) {
            console.log('✅ تأكيد: الرابط محلي 100% - لا مواقع خارجية');
        } else {
            console.error('❌ تحذير: الرابط ليس محلي!', downloadUrl);
        }
    }

    // إنشاء حاوية رسائل مؤقتة (مثل ChatGPT)
    createTemporaryChatContainer() {
        // البحث عن مكان مناسب لإضافة الحاوية
        const targetContainer = document.querySelector('main') ||
                               document.querySelector('.main-content') ||
                               document.querySelector('#app') ||
                               document.querySelector('.app') ||
                               document.body;

        // إنشاء حاوية الرسائل المؤقتة
        const tempChatContainer = document.createElement('div');
        tempChatContainer.id = 'temp-chat-container';
        tempChatContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 420px;
            max-height: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border: 1px solid #e0e0e0;
            z-index: 10000;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            animation: slideIn 0.3s ease-out;
        `;

        // إضافة CSS للأنيميشن
        if (!document.getElementById('temp-chat-styles')) {
            const styles = document.createElement('style');
            styles.id = 'temp-chat-styles';
            styles.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        // إضافة عنوان للحاوية
        const header = document.createElement('div');
        header.style.cssText = `
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 16px 20px;
            font-weight: 600;
            text-align: center;
            position: relative;
            border-radius: 16px 16px 0 0;
        `;
        header.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <span style="font-size: 20px;">📁</span>
                <span>الملفات المُنشأة</span>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="
                position: absolute;
                top: 12px;
                left: 12px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                transition: all 0.2s;
            " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
        `;

        // إضافة منطقة الرسائل
        const messagesArea = document.createElement('div');
        messagesArea.style.cssText = `
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            background: #fafafa;
        `;

        tempChatContainer.appendChild(header);
        tempChatContainer.appendChild(messagesArea);
        targetContainer.appendChild(tempChatContainer);

        console.log('✅ تم إنشاء حاوية رسائل مؤقتة احترافية');
        return messagesArea; // إرجاع منطقة الرسائل وليس الحاوية الكاملة
    }

    // وظيفة تحميل الملف المحسنة (مثل ChatGPT)
    downloadFile(url, filename) {
        console.log('📥 بدء تحميل الملف:', filename);

        try {
            // التحقق من صحة الرابط
            if (!url || !filename) {
                console.error('❌ رابط أو اسم الملف غير صحيح');
                alert('خطأ: رابط أو اسم الملف غير صحيح');
                return false;
            }

            // إنشاء رابط تحميل مخفي
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.style.display = 'none';
            downloadLink.target = '_blank'; // فتح في نافذة جديدة كبديل

            // إضافة للصفحة وتفعيل التحميل
            document.body.appendChild(downloadLink);
            downloadLink.click();

            // تنظيف الرابط بعد التحميل
            setTimeout(() => {
                if (downloadLink.parentNode) {
                    document.body.removeChild(downloadLink);
                }
                console.log('✅ تم تحميل الملف بنجاح:', filename);
            }, 100);

            return true;

        } catch (error) {
            console.error('❌ خطأ في تحميل الملف:', error);
            alert('حدث خطأ في تحميل الملف. يرجى المحاولة مرة أخرى.');
            return false;
        }
    }

    // تحميل مباشر (بديل)
    directDownload(url, filename) {
        console.log('📥 تحميل مباشر للملف:', filename);

        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = filename;
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        setTimeout(() => {
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(url);
            console.log('✅ تم التحميل المباشر بنجاح:', filename);
        }, 100);
    }

    // الحصول على أيقونة الملف حسب النوع
    getFileIcon(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊', 'csv': '📊',
            'ppt': '🎯', 'pptx': '🎯',
            'html': '🌐', 'htm': '🌐',
            'css': '🎨',
            'js': '⚡', 'javascript': '⚡',
            'py': '🐍', 'python': '🐍',
            'java': '☕',
            'cpp': '⚙️', 'c': '⚙️',
            'txt': '📄',
            'json': '📋',
            'xml': '📋',
            'zip': '📦',
            'exe': '💻'
        };
        return iconMap[extension] || '📄';
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إنشاء محتوى PDF احترافي
    generateProfessionalPDFContent(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `
        <div class="header">
            <div class="title">${topic}</div>
            <div class="subtitle">تقرير احترافي - ${timestamp}</div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📋 المحتوى الرئيسي</h2>
                <div>${this.formatContentForPDF(content)}</div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${timestamp}</p>
        </div>
        `;
    }

    // تنسيق المحتوى للـ PDF
    formatContentForPDF(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء نسخة نصية
    generateTextVersion(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `${topic}
${'='.repeat(topic.length)}

تاريخ الإنشاء: ${timestamp}
تم إنشاؤه بواسطة: المساعد التقني الذكي

المحتوى:
${'-'.repeat(50)}

${content}

${'-'.repeat(50)}
انتهى التقرير`;
    }

    // إنشاء نص العرض التقديمي
    generatePresentationText(topic, slides) {
        const timestamp = new Date().toLocaleString('ar-SA');

        let text = `عرض تقديمي: ${topic}\n`;
        text += `تاريخ الإنشاء: ${timestamp}\n`;
        text += `عدد الشرائح: ${slides.length}\n\n`;
        text += '='.repeat(50) + '\n\n';

        slides.forEach((slide, index) => {
            text += `شريحة ${index + 1}: ${slide.title}\n`;
            text += '-'.repeat(30) + '\n';
            text += `${slide.content}\n\n`;
        });

        return text;
    }

    // عرض رسالة نجاح إنشاء الملف (مع خيارات متعددة)
    showFileCreationSuccess(fileType, filename, size) {
        // تحديد طريقة العرض حسب إعدادات المستخدم
        const displayMode = this.getDisplayMode();

        switch(displayMode) {
            case 'popup':
                this.showPopupSuccess(fileType, filename, size);
                break;
            case 'chat':
                // الرابط التفاعلي يتم إضافته بواسطة addDownloadLinkToChat
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            case 'both':
                this.showPopupSuccess(fileType, filename, size);
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            default:
                // افتراضي: في المحادثة
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
        }

        console.log(`✅ تم إنشاء ${fileType}: ${filename} (${size})`);
    }

    // الحصول على طريقة العرض المفضلة
    getDisplayMode() {
        // التحقق من إعدادات المستخدم المحفوظة
        const savedMode = localStorage.getItem('fileDisplayMode');
        if (savedMode) {
            return savedMode;
        }

        // افتراضي: في المحادثة (مثل ChatGPT)
        return 'chat';
    }

    // تغيير طريقة العرض
    setDisplayMode(mode) {
        const validModes = ['popup', 'chat', 'both'];
        if (validModes.includes(mode)) {
            localStorage.setItem('fileDisplayMode', mode);
            this.showNotification(`✅ تم تغيير طريقة العرض إلى: ${this.getDisplayModeText(mode)}`, 'info');
            console.log(`📋 تم تغيير طريقة عرض الملفات إلى: ${mode}`);

            // تحديث زر طريقة العرض
            this.updateDisplayModeButton();
        } else {
            console.warn('⚠️ طريقة عرض غير صحيحة:', mode);
        }
    }

    // الحصول على نص طريقة العرض
    getDisplayModeText(mode) {
        const modeTexts = {
            'popup': 'نافذة منبثقة',
            'chat': 'في المحادثة',
            'both': 'الاثنين معاً'
        };
        return modeTexts[mode] || 'غير محدد';
    }

    // عرض نافذة منبثقة للنجاح
    showPopupSuccess(fileType, filename, size) {
        // إنشاء نافذة معاينة
        const successWindow = document.createElement('div');
        successWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px; z-index: 10000; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #28a745;
        `;

        successWindow.innerHTML = `
            <div style="color: #28a745; font-size: 48px; margin-bottom: 20px;">✅</div>
            <h2 style="color: #28a745; margin: 0 0 15px 0;">تم إنشاء الملف بنجاح!</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <p style="margin: 5px 0;"><strong>نوع الملف:</strong> ${fileType}</p>
                <p style="margin: 5px 0;"><strong>اسم الملف:</strong> ${filename}</p>
                <p style="margin: 5px 0;"><strong>الحجم:</strong> ${typeof size === 'number' ? size + ' حرف' : size}</p>
            </div>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #007bff; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إغلاق</button>
                <button onclick="window.fileCreatorInstance.showDisplayModeSettings(); this.parentElement.parentElement.remove();" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">⚙️ الإعدادات</button>
            </div>
        `;

        document.body.appendChild(successWindow);

        // إزالة النافذة تلقائياً بعد 10 ثوانٍ
        setTimeout(() => {
            if (successWindow.parentElement) {
                successWindow.remove();
            }
        }, 10000);
    }

    // عرض إعدادات طريقة العرض
    showDisplayModeSettings() {
        const currentMode = this.getDisplayMode();

        const settingsWindow = document.createElement('div');
        settingsWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px; z-index: 10001; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #007bff;
        `;

        settingsWindow.innerHTML = `
            <div style="color: #007bff; font-size: 36px; margin-bottom: 20px;">⚙️</div>
            <h2 style="color: #007bff; margin: 0 0 15px 0;">إعدادات عرض الملفات</h2>
            <p style="margin-bottom: 20px; color: #666;">اختر طريقة عرض الملفات المفضلة لديك:</p>

            <div style="text-align: right; margin: 20px 0;">
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'chat' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="chat" ${currentMode === 'chat' ? 'checked' : ''} style="margin-left: 10px;">
                    💬 في المحادثة (مثل ChatGPT)
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'popup' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="popup" ${currentMode === 'popup' ? 'checked' : ''} style="margin-left: 10px;">
                    🪟 نافذة منبثقة
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'both' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="both" ${currentMode === 'both' ? 'checked' : ''} style="margin-left: 10px;">
                    🔄 الاثنين معاً
                </label>
            </div>

            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="window.fileCreatorInstance.saveDisplayModeSettings(this.parentElement.parentElement); this.parentElement.parentElement.remove();" style="
                    background: #28a745; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">✅ حفظ</button>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إلغاء</button>
            </div>
        `;

        document.body.appendChild(settingsWindow);
    }

    // حفظ إعدادات طريقة العرض
    saveDisplayModeSettings(settingsWindow) {
        const selectedMode = settingsWindow.querySelector('input[name="displayMode"]:checked');
        if (selectedMode) {
            this.setDisplayMode(selectedMode.value);
        }
    }

    // إنشاء ملف requirements.txt
    generateRequirementsFile(programType) {
        let requirements = `# متطلبات البرنامج: ${programType}
# تم إنشاؤه بواسطة المساعد التقني الذكي

# المكتبات الأساسية
tkinter  # واجهة المستخدم الرسومية (مدمجة مع Python)

# مكتبات إضافية حسب نوع البرنامج
`;

        if (programType.includes('حاسبة') || programType.includes('calculator')) {
            requirements += `math  # العمليات الرياضية (مدمجة)
decimal  # حسابات دقيقة (مدمجة)
`;
        } else if (programType.includes('محول') || programType.includes('converter')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
shutil  # نسخ ونقل الملفات (مدمجة)
`;
        } else if (programType.includes('منظم') || programType.includes('organizer')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
datetime  # التاريخ والوقت (مدمجة)
pathlib  # مسارات الملفات (مدمجة)
`;
        }

        requirements += `
# لتحويل إلى EXE:
# pip install pyinstaller
# أو
# pip install auto-py-to-exe

# تعليمات التحويل:
# pyinstaller --onefile --windowed ${programType}.py
# أو استخدم auto-py-to-exe للواجهة الرسومية
`;

        return requirements;
    }

    // إضافة زر إعدادات طريقة العرض في الواجهة
    addDisplayModeButton() {
        try {
            // البحث عن مكان مناسب لإضافة الزر
            const controlsContainer = document.querySelector('.controls') ||
                                    document.querySelector('.buttons-container') ||
                                    document.querySelector('.chat-controls') ||
                                    document.getElementById('controls');

            if (controlsContainer) {
                // التحقق من عدم وجود الزر مسبقاً
                if (!document.getElementById('displayModeButton')) {
                    const displayModeButton = document.createElement('button');
                    displayModeButton.id = 'displayModeButton';
                    displayModeButton.innerHTML = '⚙️ طريقة العرض';
                    displayModeButton.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
                    displayModeButton.style.cssText = `
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        margin: 5px;
                        transition: all 0.3s;
                    `;

                    displayModeButton.addEventListener('click', () => {
                        this.showDisplayModeSettings();
                    });

                    displayModeButton.addEventListener('mouseenter', function() {
                        this.style.background = '#5a6268';
                        this.style.transform = 'translateY(-1px)';
                    });

                    displayModeButton.addEventListener('mouseleave', function() {
                        this.style.background = '#6c757d';
                        this.style.transform = 'translateY(0)';
                    });

                    controlsContainer.appendChild(displayModeButton);
                    console.log('✅ تم إضافة زر إعدادات طريقة العرض');
                }
            } else {
                console.log('⚠️ لم يتم العثور على حاوي الأزرار لإضافة زر إعدادات العرض');
            }
        } catch (error) {
            console.error('❌ خطأ في إضافة زر إعدادات العرض:', error);
        }
    }

    // تحديث نص زر طريقة العرض
    updateDisplayModeButton() {
        const button = document.getElementById('displayModeButton');
        if (button) {
            button.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
        }
    }

    // إنشاء ملف مع محتوى مولد من الذكاء الاصطناعي
    async createFileWithContent(fileInfo, content) {
        console.log('📁 إنشاء ملف مع محتوى مولد:', fileInfo.filename);

        try {
            // تحديد نوع MIME
            const mimeType = this.getMimeTypeForFile(fileInfo.extension);

            console.log(`📄 إنشاء ملف: ${fileInfo.filename} (${mimeType})`);
            console.log(`📊 حجم المحتوى: ${content.length} حرف`);

            // إنشاء الملف مع رابط حقيقي
            const success = this.createDownloadableFile(content, fileInfo.filename, mimeType);

            if (success) {
                // إضافة للتاريخ
                this.creationHistory.push({
                    type: fileInfo.type.toUpperCase(),
                    name: fileInfo.filename,
                    created: new Date(),
                    size: content.length,
                    aiGenerated: true
                });

                // عرض رسالة نجاح حسب طريقة العرض المختارة
                this.showFileCreationSuccess(fileInfo.type.toUpperCase(), fileInfo.filename, content.length);

                console.log('✅ تم إنشاء الملف والرابط بنجاح');

                return true; // فقط إرجاع نجاح العملية بدون رسالة
            } else {
                throw new Error('فشل في إنشاء الملف');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            throw error;
        }
    }

    // الحصول على نوع MIME للملف
    getMimeTypeForFile(extension) {
        const mimeTypes = {
            'txt': 'text/plain',
            'html': 'text/html',
            'css': 'text/css',
            'js': 'text/javascript',
            'json': 'application/json',
            'xml': 'application/xml',
            'py': 'text/x-python',
            'java': 'text/x-java-source',
            'cpp': 'text/x-c++src',
            'c': 'text/x-csrc',
            'csv': 'text/csv',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        };
        return mimeTypes[extension.toLowerCase()] || 'text/plain';
    }
}

// لا حاجة لوظيفة إضافية - الرابط HTML العادي يعمل

// تشخيص شامل قبل التصدير
console.log('🔍 FileCreatorCore.js: بدء عملية التصدير...');
console.log('🔍 نوع FileCreatorCore:', typeof FileCreatorCore);
console.log('🔍 window متاح:', typeof window !== 'undefined');

// Export for use in other modules first
if (typeof module !== 'undefined' && module.exports) {
    console.log('📦 تصدير FileCreatorCore كـ module.exports');
    module.exports = FileCreatorCore;
} else if (typeof window !== 'undefined') {
    console.log('🌐 تصدير FileCreatorCore إلى window');
    window.FileCreatorCore = FileCreatorCore;
    console.log('✅ تم تصدير FileCreatorCore إلى window');
    console.log('🔍 window.FileCreatorCore:', typeof window.FileCreatorCore);
} else {
    console.error('❌ لا يمكن تصدير FileCreatorCore - لا module ولا window متاح');
}

// Create global instance after export
console.log('📁 إنشاء مثيل File Creator تلقائي...');
try {
    if (typeof window !== 'undefined' && window.FileCreatorCore) {
        window.fileCreatorInstance = new window.FileCreatorCore();
        console.log('✅ تم إنشاء window.fileCreatorInstance بنجاح');
        console.log('🔍 نوع fileCreatorInstance:', typeof window.fileCreatorInstance);
    } else {
        console.error('❌ لا يمكن إنشاء المثيل - window.FileCreatorCore غير متاح');
    }
} catch (error) {
    console.error('❌ فشل في إنشاء fileCreatorInstance:', error);
}

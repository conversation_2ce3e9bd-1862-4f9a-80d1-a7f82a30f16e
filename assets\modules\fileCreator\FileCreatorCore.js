/**
 * File Creator Core - Advanced File Generation System
 * Creates professional PDF, PowerPoint, EXE files and more
 * Integrated with internet capabilities like ChatGPT Pro
 */

// تصدير فوري للكلاس قبل التعريف
console.log('📁 بدء تحميل FileCreatorCore...');
alert('📁 FileCreatorCore.js يتم تحميله الآن!');

class FileCreatorCore {

    constructor() {
        this.isActive = false;
        this.internetAccess = true;
        this.creationHistory = [];
        this.templates = this.initTemplates();
        this.supportedFormats = [
            'pdf', 'pptx', 'docx', 'xlsx', 'exe', 'html', 'css', 'js',
            'py', 'java', 'cpp', 'zip', 'json', 'xml', 'csv'
        ];
        this.internetTools = this.initInternetTools();
        this.loadJsPDF();
    }

    // تحميل مكتبة jsPDF للنظام الأصلي
    loadJsPDF() {
        if (typeof window.jsPDF === 'undefined') {
            console.log('📦 تحميل مكتبة jsPDF للنظام الأصلي...');
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل مكتبة jsPDF للنظام الأصلي');
                // التأكد من تعيين jsPDF بشكل صحيح
                if (window.jspdf && window.jspdf.jsPDF) {
                    window.jsPDF = window.jspdf.jsPDF;
                    console.log('✅ تم تعيين window.jsPDF بنجاح');
                } else {
                    console.warn('⚠️ مشكلة في تعيين jsPDF');
                }
            };
            script.onerror = () => {
                console.warn('⚠️ فشل في تحميل jsPDF للنظام الأصلي');
            };
            document.head.appendChild(script);
        } else {
            console.log('✅ مكتبة jsPDF متاحة بالفعل');
        }
    }

    // Initialize templates for different file types
    initTemplates() {
        return {
            pdf: {
                report: 'تقرير احترافي',
                presentation: 'عرض تقديمي',
                manual: 'دليل المستخدم',
                invoice: 'فاتورة',
                certificate: 'شهادة',
                resume: 'سيرة ذاتية'
            },
            powerpoint: {
                business: 'عرض أعمال',
                educational: 'عرض تعليمي',
                technical: 'عرض تقني',
                marketing: 'عرض تسويقي',
                scientific: 'عرض علمي'
            },
            exe: {
                utility: 'أداة مساعدة',
                game: 'لعبة بسيطة',
                calculator: 'آلة حاسبة',
                converter: 'محول ملفات',
                organizer: 'منظم ملفات'
            }
        };
    }

    // Initialize internet tools
    initInternetTools() {
        return {
            imageSearch: true,
            dataFetching: true,
            apiAccess: true,
            downloadCapability: true,
            realTimeInfo: true
        };
    }

    // Activate File Creator Mode
    activate() {
        this.isActive = true;
        console.log('📁 File Creator Mode activated');

        // تحديث المتغير العام
        window.fileCreatorActive = true;

        // إظهار تأكيد مرئي فوري
        this.showActivationConfirmation();

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '✅ تم تفعيل File Creator Mode - جاهز لإنشاء الملفات مع روابط حقيقية مثل ChatGPT');
        }

        if (typeof speakText === 'function') {
            speakText('تم تفعيل File Creator Mode المتقدم. يمكنني الآن إنشاء أي نوع من الملفات مع التكامل الكامل مع الإنترنت. ما الملف الذي تريد إنشاءه؟');
        }
    }

    // Handle voice commands for file creation
    async handleVoiceCommand(command) {
        const lowerCommand = command.toLowerCase();

        // إعدادات طريقة العرض (أوامر صوتية)
        if (lowerCommand.includes('غير طريقة العرض') || lowerCommand.includes('إعدادات الملفات') ||
            lowerCommand.includes('اعدادات العرض') || lowerCommand.includes('طريقة عرض الملفات')) {
            this.showDisplayModeSettings();
            return '⚙️ تم فتح إعدادات طريقة عرض الملفات. يمكنك الاختيار بين: المحادثة، نافذة منبثقة، أو الاثنين معاً.';
        }

        if (lowerCommand.includes('عرض في المحادثة') || lowerCommand.includes('مثل شات جي بي تي') ||
            lowerCommand.includes('في الدردشة') || lowerCommand.includes('chat mode')) {
            this.setDisplayMode('chat');
            return '💬 تم تغيير طريقة العرض إلى: في المحادثة مثل ChatGPT. الملفات ستظهر كروابط تحميل في المحادثة.';
        }

        if (lowerCommand.includes('عرض نافذة منبثقة') || lowerCommand.includes('نافذة منبثقة') ||
            lowerCommand.includes('popup') || lowerCommand.includes('نافذة')) {
            this.setDisplayMode('popup');
            return '🪟 تم تغيير طريقة العرض إلى: نافذة منبثقة. ستظهر نافذة تأكيد عند إنشاء الملفات.';
        }

        if (lowerCommand.includes('عرض الاثنين') || lowerCommand.includes('الطريقتين') ||
            lowerCommand.includes('both') || lowerCommand.includes('معا')) {
            this.setDisplayMode('both');
            return '🔄 تم تغيير طريقة العرض إلى: الاثنين معاً. ستحصل على نافذة منبثقة ورابط في المحادثة.';
        }

        // PDF creation commands
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) {
            return await this.handlePDFCreation(command);
        }

        // PowerPoint creation commands
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) {
            return await this.handlePowerPointCreation(command);
        }

        // EXE creation commands
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج') || lowerCommand.includes('تطبيق')) {
            return await this.handleEXECreation(command);
        }

        // General file creation
        if (lowerCommand.includes('أنشئ') || lowerCommand.includes('اعمل') || lowerCommand.includes('create')) {
            return await this.handleGeneralFileCreation(command);
        }

        // Internet integration commands
        if (lowerCommand.includes('من الإنترنت') || lowerCommand.includes('ابحث') || lowerCommand.includes('حمل')) {
            return await this.handleInternetIntegration(command);
        }

        return await this.getAIFileCreationResponse(command);
    }

    // معالجة طلبات المستخدم (للتكامل مع المحادثة)
    async processUserRequest(userMessage) {
        console.log('📁 معالجة طلب المستخدم:', userMessage.substring(0, 50));

        try {
            // التحقق من التفعيل باستخدام المتغير العام الموثوق
            const isActive = window.fileCreatorActive === true || this.isActive === true;

            if (!isActive) {
                console.log('⚠️ File Creator غير مفعل');
                console.log('- this.isActive:', this.isActive);
                console.log('- window.fileCreatorActive:', window.fileCreatorActive);
                return 'يرجى تفعيل File Creator Mode أولاً من الأزرار العلوية';
            }

            console.log('✅ File Creator مفعل - بدء معالجة الطلب');

            // تحليل نوع الملف المطلوب
            const lowerMessage = userMessage.toLowerCase();

            if (lowerMessage.includes('pdf') || lowerMessage.includes('تقرير')) {
                return await this.handlePDFCreation(userMessage);
            } else if (lowerMessage.includes('powerpoint') || lowerMessage.includes('عرض') || lowerMessage.includes('ppt')) {
                return await this.handlePowerPointCreation(userMessage);
            } else if (lowerMessage.includes('exe') || lowerMessage.includes('برنامج') || lowerMessage.includes('تطبيق')) {
                return await this.handleEXECreation(userMessage);
            } else {
                // ملف عام - تحديد النوع تلقائياً
                return await this.handleGeneralFileCreation(userMessage);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة طلب المستخدم:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // معالجة إنشاء الملفات العامة
    async handleGeneralFileCreation(userMessage) {
        console.log('📄 معالجة إنشاء ملف عام:', userMessage.substring(0, 50));

        try {
            // استخدام detectFileType المحسن
            const detectedType = this.detectFileType(userMessage);

            // تحويل نوع الملف إلى معلومات التحميل
            const fileInfo = this.getFileTypeInfo(detectedType);
            const fileType = fileInfo.type;
            const mimeType = fileInfo.mimeType;
            const extension = fileInfo.extension;

            // استخراج الموضوع
            const topic = this.extractTopic(userMessage);
            const filename = `${topic}${extension}`;

            // إنشاء المحتوى
            const content = await this.generateFileContent(userMessage, fileType);

            if (content) {
                // إنشاء الملف مع الحاوية الاحترافية مباشرة
                console.log('📁 إنشاء الملف بالنظام الأصلي:', filename);

                // معالجة خاصة لـ PDF
                if (fileType === 'pdf') {
                    if (content instanceof Blob) {
                        // PDF حقيقي من jsPDF
                        const url = URL.createObjectURL(content);
                        console.log('📊 تفاصيل PDF الحقيقي:', { filename, size: content.size, type: 'application/pdf' });
                        this.createProfessionalDownloadContainer(filename, url, content.size);
                    } else if (content instanceof Promise) {
                        // انتظار إنشاء PDF
                        console.log('⏳ انتظار إنشاء PDF...');
                        const pdfBlob = await content;
                        if (pdfBlob instanceof Blob) {
                            const url = URL.createObjectURL(pdfBlob);
                            console.log('📊 تفاصيل PDF المنتظر:', { filename, size: pdfBlob.size, type: 'application/pdf' });
                            this.createProfessionalDownloadContainer(filename, url, pdfBlob.size);
                        } else {
                            // PDF نصي
                            const blob = new Blob([pdfBlob], { type: 'application/pdf' });
                            const url = URL.createObjectURL(blob);
                            console.log('📊 تفاصيل PDF النصي:', { filename, size: blob.size, type: 'application/pdf' });
                            this.createProfessionalDownloadContainer(filename, url, blob.size);
                        }
                    } else {
                        // PDF نصي
                        const blob = new Blob([content], { type: 'application/pdf' });
                        const url = URL.createObjectURL(blob);
                        console.log('📊 تفاصيل PDF النصي:', { filename, size: blob.size, type: 'application/pdf' });
                        this.createProfessionalDownloadContainer(filename, url, blob.size);
                    }
                } else {
                    // ملفات أخرى
                    const blob = new Blob([content], { type: mimeType });
                    const url = URL.createObjectURL(blob);
                    console.log('📊 تفاصيل الملف الأصلي:', { filename, size: blob.size, type: mimeType });
                    this.createProfessionalDownloadContainer(filename, url, blob.size);
                }

                return `✅ **تم إنشاء الملف بنجاح!**

📄 **الملف:** ${filename}
📊 **النوع:** ${fileType.toUpperCase()}
📝 **المحتوى:** ${content.length} حرف
🎯 **الحالة:** جاهز للتحميل

💡 **تم فتح نافذة التحميل الاحترافية!** 🎉`;
            } else {
                return '❌ لم يتم توليد محتوى للملف. تأكد من تشغيل النموذج المحلي.';
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف العام:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // توليد محتوى الملف باستخدام أي نموذج متاح
    async generateFileContent(userMessage, fileType) {
        // إنشاء prompt مفصل ومخصص
        const prompt = this.createDetailedPrompt(userMessage, fileType);

        console.log('🎯 النظام الأصلي: توليد محتوى مخصص للطلب:', userMessage);

        try {
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتوليد المحتوى...');
                try {
                    const response = await window.openRouterIntegration.smartSendMessage(prompt, {
                        mode: 'file_creator',
                        temperature: 0.7,
                        maxTokens: 2000
                    });
                    if (response && response.text && response.text.trim()) {
                        content = response.text.trim();
                        console.log('✅ تم توليد المحتوى بواسطة OpenRouter');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في OpenRouter:', error);
                }
            }

            // ثانياً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتوليد المحتوى...');
                try {
                    const localResponse = await technicalAssistant.getResponse(prompt);
                    if (localResponse && localResponse.trim()) {
                        content = localResponse.trim();
                        console.log('✅ تم توليد المحتوى بواسطة النموذج المحلي');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المحلي:', error);
                }
            }

            // ثالثاً: جرب أي نموذج آخر متاح (GPT, Claude, Gemini...)
            if (!content) {
                console.log('🔍 البحث عن نماذج أخرى متاحة...');

                // جرب getAIModelResponse إذا كان متاحاً
                if (typeof window.getAIModelResponse === 'function') {
                    try {
                        const aiResponse = await window.getAIModelResponse(prompt);
                        if (aiResponse && aiResponse.trim()) {
                            content = aiResponse.trim();
                            console.log('✅ تم توليد المحتوى بواسطة نموذج AI آخر');
                        }
                    } catch (error) {
                        console.warn('⚠️ خطأ في نموذج AI آخر:', error);
                    }
                }

                // جرب أي دالة أخرى متاحة
                if (!content && typeof window.generateAIContent === 'function') {
                    try {
                        const generatedContent = await window.generateAIContent(prompt);
                        if (generatedContent && generatedContent.trim()) {
                            content = generatedContent.trim();
                            console.log('✅ تم توليد المحتوى بواسطة generateAIContent');
                        }
                    } catch (error) {
                        console.warn('⚠️ خطأ في generateAIContent:', error);
                    }
                }
            }

            // رابعاً: محتوى مخصص كحل أخير
            if (!content) {
                console.log('📝 إنشاء محتوى مخصص كحل أخير...');
                content = this.generateCustomContentFallback(userMessage, fileType);
            }

            // التأكد من أن المحتوى ليس فارغاً
            if (!content || content.trim().length === 0) {
                console.warn('⚠️ فشل في توليد أي محتوى، إنشاء محتوى أساسي');
                content = this.generateBasicCustomContent(userMessage, fileType);
            }

            console.log('✅ النظام الأصلي: تم توليد المحتوى بنجاح، الطول:', content.length);
            return content;

        } catch (error) {
            console.error('❌ خطأ في توليد المحتوى:', error);
            return this.generateDefaultContent(userMessage, fileType);
        }
    }

    // توليد محتوى افتراضي ذكي ومفصل حسب الطلب
    async generateDefaultContent(userMessage, fileType) {
        const topic = this.extractTopic(userMessage);

        // استخراج الموضوع الحقيقي من الطلب
        const realTopic = this.extractRealTopic(userMessage);

        console.log('📝 إنشاء محتوى ذكي:', { topic, realTopic, fileType, userMessage: userMessage.substring(0, 100) });

        // إنشاء محتوى ذكي حسب الموضوع المطلوب
        const smartContent = this.generateSmartContentByTopic(realTopic, userMessage);

        switch (fileType) {
            case 'html':
                return this.generateSmartHTMLContent(realTopic, userMessage, smartContent);

            case 'css':
                return this.generateSmartCSSContent(realTopic, userMessage);

            case 'xml':
                return this.generateSmartXMLContent(realTopic, userMessage);

            case 'pdf':
                // إنشاء PDF حقيقي باستخدام jsPDF
                return this.createRealPDFContent(smartContent, userMessage, realTopic);

            case 'javascript':
                return this.generateSmartJavaScriptContent(realTopic, userMessage);

            case 'python':
                return this.generateSmartPythonContent(realTopic, userMessage);

            case 'json':
                return this.generateSmartJSONContent(realTopic, userMessage);

            default:
                // استخدام المحتوى النصي الذكي
                return `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الطلب الأصلي: ${userMessage}
===============================================`;

        }
    }

    // إنشاء PDF حقيقي باستخدام jsPDF
    createRealPDFContent(smartContent, userMessage, realTopic) {
        // التحقق من توفر jsPDF مع انتظار قصير إذا لزم الأمر
        return new Promise((resolve) => {
            const checkJsPDF = () => {
                if (typeof window.jsPDF !== 'undefined') {
                    try {
                        console.log('📄 إنشاء PDF حقيقي باستخدام jsPDF...');

                        const doc = new window.jsPDF({
                            orientation: 'portrait',
                            unit: 'mm',
                            format: 'a4'
                        });

                        // إعداد الخط
                        doc.setFont('helvetica');
                        doc.setFontSize(18);

                        // العنوان الرئيسي
                        const title = smartContent.title || realTopic || 'مستند PDF';
                        doc.text(title, 20, 30);

                        // خط تحت العنوان
                        doc.setLineWidth(0.5);
                        doc.line(20, 35, 190, 35);

                        // المحتوى الرئيسي
                        doc.setFontSize(12);
                        let yPosition = 50;

                        // تقسيم النص إلى أسطر
                        const content = smartContent.textContent || 'محتوى PDF احترافي تم إنشاؤه بواسطة المساعد التقني الذكي';
                        const lines = doc.splitTextToSize(content, 170);

                        // إضافة النص مع التحكم في المسافات
                        lines.forEach((line, index) => {
                            if (yPosition > 270) { // إضافة صفحة جديدة إذا لزم الأمر
                                doc.addPage();
                                yPosition = 20;
                            }
                            doc.text(line, 20, yPosition);
                            yPosition += 7;
                        });

                        // معلومات إضافية في أسفل الصفحة
                        yPosition += 20;
                        if (yPosition > 270) {
                            doc.addPage();
                            yPosition = 20;
                        }

                        doc.setFontSize(10);
                        doc.setTextColor(100);
                        doc.text('تم إنشاء هذا الملف بواسطة المساعد التقني الذكي', 20, yPosition);
                        doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-SA')}`, 20, yPosition + 10);
                        doc.text(`الطلب: ${userMessage.substring(0, 60)}...`, 20, yPosition + 20);

                        // تحويل إلى Blob مع MIME type صحيح
                        const pdfBlob = doc.output('blob');
                        console.log('✅ تم إنشاء PDF حقيقي بحجم:', pdfBlob.size, 'بايت');
                        resolve(pdfBlob);

                    } catch (error) {
                        console.error('❌ خطأ في إنشاء PDF بـ jsPDF:', error);
                        resolve(this.createTextBasedPDF(smartContent, userMessage, realTopic));
                    }
                } else {
                    // انتظار قصير ثم إعادة المحاولة
                    setTimeout(() => {
                        if (typeof window.jsPDF !== 'undefined') {
                            checkJsPDF();
                        } else {
                            console.warn('⚠️ jsPDF غير متاح، استخدام النظام النصي');
                            resolve(this.createTextBasedPDF(smartContent, userMessage, realTopic));
                        }
                    }, 500);
                }
            };

            checkJsPDF();
        });
    }

    // إنشاء PDF نصي كنظام بديل
    createTextBasedPDF(smartContent, userMessage, realTopic) {
        return `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الطلب الأصلي: ${userMessage}
===============================================`;
    }

    // إنشاء محتوى JavaScript ذكي
    generateSmartJavaScriptContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) {
            return `/**
 * ${realTopic} - حاسبة JavaScript احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(a + ' + ' + b + ' = ' + result);
        return result;
    }

    subtract(a, b) {
        const result = a - b;
        this.history.push(a + ' - ' + b + ' = ' + result);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        this.history.push(a + ' × ' + b + ' = ' + result);
        return result;
    }

    divide(a, b) {
        if (b === 0) throw new Error('لا يمكن القسمة على صفر');
        const result = a / b;
        this.history.push(a + ' ÷ ' + b + ' = ' + result);
        return result;
    }

    getHistory() {
        return this.history;
    }

    clear() {
        this.result = 0;
        this.history = [];
    }
}

// استخدام الحاسبة
const calc = new Calculator();
console.log('مرحباً من ${realTopic}');
console.log('الجمع:', calc.add(10, 5));
console.log('الطرح:', calc.subtract(10, 3));
console.log('التاريخ:', calc.getHistory());`;
        } else if (lowerMessage.includes('لعبة') || lowerMessage.includes('game')) {
            return `/**
 * ${realTopic} - لعبة JavaScript بسيطة
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class SimpleGame {
    constructor() {
        this.score = 0;
        this.level = 1;
        this.isPlaying = false;
    }

    start() {
        this.isPlaying = true;
        this.score = 0;
        this.level = 1;
        console.log('🎮 بدء اللعبة!');
        console.log('مرحباً بك في ${realTopic}');
        this.gameLoop();
    }

    gameLoop() {
        if (!this.isPlaying) return;

        // محاكاة دورة اللعبة
        this.score += Math.floor(Math.random() * 10) + 1;
        console.log(\`النقاط: \${this.score} | المستوى: \${this.level}\`);

        if (this.score > this.level * 50) {
            this.levelUp();
        }

        // استمرار اللعبة
        setTimeout(() => this.gameLoop(), 1000);
    }

    levelUp() {
        this.level++;
        console.log(\`🎉 تهانينا! وصلت للمستوى \${this.level}\`);
    }

    stop() {
        this.isPlaying = false;
        console.log(\`🏁 انتهت اللعبة! النقاط النهائية: \${this.score}\`);
    }
}

// تشغيل اللعبة
const game = new SimpleGame();
game.start();

// إيقاف اللعبة بعد 10 ثوان
setTimeout(() => game.stop(), 10000);`;
        } else {
            return `/**
 * ${realTopic} - JavaScript Application
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class ${realTopic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${realTopic}';
        this.version = '1.0.0';
        this.isInitialized = false;
    }

    init() {
        console.log(\`تهيئة \${this.name}...\`);
        this.isInitialized = true;
        console.log('✅ تم تهيئة التطبيق بنجاح');
        return this;
    }

    run() {
        if (!this.isInitialized) {
            console.log('⚠️ يجب تهيئة التطبيق أولاً');
            return false;
        }
        console.log(\`🚀 تشغيل \${this.name}...\`);
        this.mainFunction();
        return true;
    }

    mainFunction() {
        console.log('تنفيذ الوظيفة الرئيسية...');
        // إضافة منطق التطبيق هنا
        console.log('✅ تم تنفيذ العملية بنجاح');
    }

    getInfo() {
        return {
            name: this.name,
            version: this.version,
            status: this.isInitialized ? 'جاهز' : 'غير مهيأ'
        };
    }
}

// تشغيل التطبيق
const app = new ${realTopic.replace(/\s+/g, '')}App();
console.log('مرحباً من ${realTopic}');
app.init().run();
console.log('معلومات التطبيق:', app.getInfo());`;
        }
    }

    // إنشاء محتوى Python ذكي
    generateSmartPythonContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('حذف ملف') || lowerMessage.includes('delete file')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - برنامج حذف الملفات
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
from pathlib import Path

def delete_file(file_path):
    """حذف ملف معين من النظام"""
    try:
        file_path = Path(file_path)
        if file_path.exists():
            file_path.unlink()
            print(f"✅ تم حذف الملف بنجاح: {file_path}")
            return True
        else:
            print(f"❌ الملف غير موجود: {file_path}")
            return False
    except PermissionError:
        print(f"❌ ليس لديك صلاحية لحذف هذا الملف: {file_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في حذف الملف: {e}")
        return False

def delete_multiple_files(file_list):
    """حذف عدة ملفات"""
    deleted_count = 0
    for file_path in file_list:
        if delete_file(file_path):
            deleted_count += 1
    return deleted_count

def main():
    """الوظيفة الرئيسية"""
    print("🗑️ برنامج ${realTopic}")
    print("=" * 40)

    while True:
        print("\\n1. حذف ملف واحد")
        print("2. حذف عدة ملفات")
        print("3. الخروج")

        choice = input("اختر العملية (1-3): ").strip()

        if choice == '1':
            file_path = input("أدخل مسار الملف: ").strip()
            if file_path:
                confirm = input(f"هل أنت متأكد من حذف '{file_path}'؟ (y/n): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    delete_file(file_path)
                else:
                    print("تم إلغاء العملية")

        elif choice == '2':
            print("أدخل مسارات الملفات (اضغط Enter مرتين للانتهاء):")
            file_list = []
            while True:
                file_path = input("مسار الملف: ").strip()
                if not file_path:
                    break
                file_list.append(file_path)

            if file_list:
                print(f"سيتم حذف {len(file_list)} ملف(ات)")
                confirm = input("هل أنت متأكد؟ (y/n): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    deleted = delete_multiple_files(file_list)
                    print(f"تم حذف {deleted} من أصل {len(file_list)} ملف")

        elif choice == '3':
            print("شكراً لاستخدام ${realTopic}")
            break

        else:
            print("خيار غير صحيح")

if __name__ == "__main__":
    main()`;
        } else if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - حاسبة Python احترافية
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import math
import operator

class Calculator:
    def __init__(self):
        self.history = []
        self.operations = {
            '+': operator.add,
            '-': operator.sub,
            '*': operator.mul,
            '/': operator.truediv,
            '**': operator.pow,
            '%': operator.mod
        }

    def calculate(self, num1, operation, num2):
        """تنفيذ العملية الحسابية"""
        try:
            if operation == '/' and num2 == 0:
                raise ValueError("لا يمكن القسمة على صفر")

            result = self.operations[operation](num1, num2)
            self.history.append(f"{num1} {operation} {num2} = {result}")
            return result
        except Exception as e:
            print(f"خطأ في العملية: {e}")
            return None

    def scientific_operation(self, operation, number):
        """العمليات العلمية"""
        try:
            if operation == 'sqrt':
                if number < 0:
                    raise ValueError("لا يمكن حساب الجذر التربيعي لرقم سالب")
                result = math.sqrt(number)
            elif operation == 'sin':
                result = math.sin(math.radians(number))
            elif operation == 'cos':
                result = math.cos(math.radians(number))
            elif operation == 'tan':
                result = math.tan(math.radians(number))
            elif operation == 'log':
                if number <= 0:
                    raise ValueError("لا يمكن حساب اللوغاريتم لرقم سالب أو صفر")
                result = math.log10(number)
            else:
                raise ValueError("عملية غير مدعومة")

            self.history.append(f"{operation}({number}) = {result}")
            return result
        except Exception as e:
            print(f"خطأ في العملية العلمية: {e}")
            return None

    def show_history(self):
        """عرض تاريخ العمليات"""
        if not self.history:
            print("لا توجد عمليات في التاريخ")
        else:
            print("\\nتاريخ العمليات:")
            for i, operation in enumerate(self.history, 1):
                print(f"{i}. {operation}")

    def clear_history(self):
        """مسح التاريخ"""
        self.history.clear()
        print("تم مسح التاريخ")

def main():
    """الوظيفة الرئيسية"""
    calc = Calculator()
    print("🧮 مرحباً بك في ${realTopic}")
    print("=" * 40)

    while True:
        print("\\n1. عملية حسابية بسيطة")
        print("2. عملية علمية")
        print("3. عرض التاريخ")
        print("4. مسح التاريخ")
        print("5. الخروج")

        choice = input("اختر العملية (1-5): ").strip()

        if choice == '1':
            try:
                num1 = float(input("أدخل الرقم الأول: "))
                operation = input("أدخل العملية (+, -, *, /, **, %): ").strip()
                num2 = float(input("أدخل الرقم الثاني: "))

                if operation in calc.operations:
                    result = calc.calculate(num1, operation, num2)
                    if result is not None:
                        print(f"النتيجة: {result}")
                else:
                    print("عملية غير مدعومة")
            except ValueError:
                print("يرجى إدخال أرقام صحيحة")

        elif choice == '2':
            try:
                print("العمليات المتاحة: sqrt, sin, cos, tan, log")
                operation = input("أدخل العملية: ").strip().lower()
                number = float(input("أدخل الرقم: "))

                result = calc.scientific_operation(operation, number)
                if result is not None:
                    print(f"النتيجة: {result}")
            except ValueError:
                print("يرجى إدخال رقم صحيح")

        elif choice == '3':
            calc.show_history()

        elif choice == '4':
            calc.clear_history()

        elif choice == '5':
            print("شكراً لاستخدام ${realTopic}")
            break

        else:
            print("خيار غير صحيح")

if __name__ == "__main__":
    main()`;
        } else {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - Python Application
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
import datetime

class ${realTopic.replace(/\s+/g, '')}App:
    """فئة التطبيق الرئيسية"""

    def __init__(self):
        self.name = "${realTopic}"
        self.version = "1.0.0"
        self.created_at = datetime.datetime.now()
        self.is_running = False

    def display_info(self):
        """عرض معلومات التطبيق"""
        print(f"📱 اسم التطبيق: {self.name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ الإنشاء: {self.created_at}")
        print(f"🔄 الحالة: {'يعمل' if self.is_running else 'متوقف'}")

    def main_function(self):
        """الوظيفة الرئيسية للتطبيق"""
        print(f"🚀 تشغيل {self.name}...")
        print("⚙️ معالجة البيانات...")

        # محاكاة معالجة
        import time
        for i in range(1, 6):
            print(f"خطوة {i}/5 - معالجة...")
            time.sleep(0.5)

        print("✅ تم تنفيذ الوظيفة الرئيسية بنجاح!")
        return True

    def run(self):
        """تشغيل التطبيق"""
        print("=" * 50)
        print(f"🐍 مرحباً بك في {self.name}")
        print("=" * 50)

        self.is_running = True
        self.display_info()
        print("\\n" + "-" * 30)

        try:
            result = self.main_function()
            if result:
                print("\\n✅ تم إنهاء التطبيق بنجاح")
            else:
                print("\\n❌ حدث خطأ في التطبيق")
        except Exception as e:
            print(f"\\n❌ خطأ: {e}")
        finally:
            self.is_running = False

def main():
    """نقطة دخول البرنامج"""
    app = ${realTopic.replace(/\s+/g, '')}App()
    app.run()

if __name__ == "__main__":
    main()`;
        }
    }

    // إنشاء محتوى JSON ذكي
    generateSmartJSONContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "version": "1.0.0",
                "categories": [
                    {
                        "id": 1,
                        "name": "المبادئ الأساسية",
                        "items": [
                            { "principle": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                            { "principle": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                            { "principle": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                        ]
                    },
                    {
                        "id": 2,
                        "name": "التهديدات الشائعة",
                        "items": [
                            { "threat": "البرمجيات الخبيثة", "severity": "عالي", "prevention": "برامج مكافحة الفيروسات" },
                            { "threat": "التصيد الاحتيالي", "severity": "متوسط", "prevention": "التوعية والتدريب" },
                            { "threat": "هجمات DDoS", "severity": "عالي", "prevention": "جدران الحماية المتقدمة" }
                        ]
                    }
                ],
                "tools": [
                    { "name": "جدران الحماية", "type": "network", "effectiveness": "عالي" },
                    { "name": "التشفير", "type": "data", "effectiveness": "عالي جداً" },
                    { "name": "المصادقة متعددة العوامل", "type": "authentication", "effectiveness": "عالي" }
                ],
                "best_practices": [
                    "استخدام كلمات مرور قوية",
                    "تحديث البرامج بانتظام",
                    "عمل نسخ احتياطية دورية",
                    "تدريب الموظفين على الأمان"
                ]
            }, null, 2);
        } else if (lowerMessage.includes('منتج') || lowerMessage.includes('كتالوج')) {
            return JSON.stringify({
                "catalog": {
                    "name": realTopic,
                    "version": "1.0",
                    "created": new Date().toISOString(),
                    "products": [
                        {
                            "id": 1,
                            "name": "منتج تجريبي 1",
                            "description": "وصف المنتج الأول",
                            "price": {
                                "amount": 100.00,
                                "currency": "SAR"
                            },
                            "category": "إلكترونيات",
                            "availability": true,
                            "specifications": {
                                "color": "أسود",
                                "weight": "1.5 كيلو",
                                "warranty": "سنة واحدة"
                            },
                            "images": [
                                "product1_main.jpg",
                                "product1_side.jpg"
                            ]
                        },
                        {
                            "id": 2,
                            "name": "منتج تجريبي 2",
                            "description": "وصف المنتج الثاني",
                            "price": {
                                "amount": 250.00,
                                "currency": "SAR"
                            },
                            "category": "أجهزة",
                            "availability": false,
                            "specifications": {
                                "color": "أبيض",
                                "weight": "2.0 كيلو",
                                "warranty": "سنتان"
                            }
                        }
                    ],
                    "categories": [
                        { "id": 1, "name": "إلكترونيات", "count": 1 },
                        { "id": 2, "name": "أجهزة", "count": 1 }
                    ],
                    "metadata": {
                        "total_products": 2,
                        "last_updated": new Date().toISOString(),
                        "currency": "SAR"
                    }
                }
            }, null, 2);
        } else {
            // JSON عام
            return JSON.stringify({
                "name": realTopic,
                "description": `ملف JSON تم إنشاؤه حول ${realTopic}`,
                "created": new Date().toISOString(),
                "version": "1.0.0",
                "metadata": {
                    "author": "المساعد التقني الذكي",
                    "request": userMessage,
                    "type": "general_data"
                },
                "content": {
                    "title": realTopic,
                    "sections": [
                        {
                            "id": 1,
                            "name": "مقدمة",
                            "description": `مقدمة شاملة حول ${realTopic}`,
                            "items": [
                                `النقطة الأولى حول ${realTopic}`,
                                `النقطة الثانية حول ${realTopic}`,
                                `النقطة الثالثة حول ${realTopic}`
                            ]
                        },
                        {
                            "id": 2,
                            "name": "التفاصيل",
                            "description": `تفاصيل مفصلة حول ${realTopic}`,
                            "properties": {
                                "type": realTopic,
                                "status": "active",
                                "priority": "high"
                            }
                        }
                    ]
                },
                "resources": [
                    { "type": "كتاب", "title": `كتاب عن ${realTopic}`, "recommended": true },
                    { "type": "موقع", "title": `موقع متخصص في ${realTopic}`, "recommended": true }
                ]
            }, null, 2);
        }
    }

    // إنشاء محتوى JSON ذكي
    generateSmartJSONContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "version": "1.0.0",
                "security_principles": [
                    { "principle": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                    { "principle": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                    { "principle": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                ],
                "threats": [
                    { "name": "البرمجيات الخبيثة", "severity": "عالي", "prevention": "برامج مكافحة الفيروسات" },
                    { "name": "التصيد الاحتيالي", "severity": "متوسط", "prevention": "التوعية والتدريب" }
                ],
                "tools": [
                    { "name": "جدران الحماية", "type": "network", "effectiveness": "عالي" },
                    { "name": "التشفير", "type": "data", "effectiveness": "عالي جداً" }
                ]
            }, null, 2);
        } else {
            return JSON.stringify({
                "name": realTopic,
                "description": `ملف JSON تم إنشاؤه حول ${realTopic}`,
                "created": new Date().toISOString(),
                "version": "1.0.0",
                "metadata": {
                    "author": "المساعد التقني الذكي",
                    "request": userMessage
                },
                "content": {
                    "title": realTopic,
                    "sections": [
                        { "name": "مقدمة", "description": `مقدمة شاملة حول ${realTopic}` },
                        { "name": "التفاصيل", "description": `تفاصيل مفصلة حول ${realTopic}` }
                    ]
                }
            }, null, 2);
        }
    }

    // إنشاء prompt مفصل ومخصص حسب الطلب
    createDetailedPrompt(userMessage, fileType) {
        const lowerMessage = userMessage.toLowerCase();

        // تحليل الطلب لاستخراج التفاصيل
        const requestAnalysis = {
            topic: this.extractTopicFromRequest(userMessage),
            intent: this.analyzeUserIntent(lowerMessage),
            specificRequirements: this.extractSpecificRequirements(lowerMessage),
            fileType: fileType
        };

        console.log('🔍 النظام الأصلي: تحليل الطلب:', requestAnalysis);

        // بناء prompt مخصص حسب نوع الملف والطلب
        let prompt = `أنت خبير متخصص في إنشاء ${fileType} files.

الطلب المحدد من المستخدم: "${userMessage}"

الموضوع المستخرج: ${requestAnalysis.topic}
نوع الطلب: ${requestAnalysis.intent}
المتطلبات الخاصة: ${requestAnalysis.specificRequirements.join(', ') || 'لا توجد'}

`;

        // إضافة تعليمات مخصصة حسب نوع الملف
        switch(fileType) {
            case 'pdf':
                prompt += `قم بإنشاء محتوى PDF احترافي يتضمن:
- عنوان واضح ومناسب للموضوع
- مقدمة شاملة
- محتوى مفصل ومنظم في أقسام
- معلومات دقيقة وحديثة
- خاتمة مفيدة
- تنسيق مناسب للطباعة`;
                break;

            case 'javascript':
                prompt += `قم بإنشاء كود JavaScript وظيفي يتضمن:
- كود نظيف ومنظم
- تعليقات واضحة باللغة العربية
- وظائف عملية تحقق الهدف المطلوب
- معالجة للأخطاء
- أمثلة على الاستخدام
- كود قابل للتنفيذ مباشرة`;
                break;

            case 'python':
                prompt += `قم بإنشاء كود Python وظيفي يتضمن:
- كود احترافي ومنظم
- docstrings باللغة العربية
- معالجة شاملة للأخطاء
- وظائف تحقق الهدف المطلوب بدقة
- أمثلة على الاستخدام
- كود قابل للتنفيذ`;
                break;

            case 'html':
                prompt += `قم بإنشاء صفحة HTML كاملة تتضمن:
- هيكل HTML5 صحيح
- تصميم CSS مدمج احترافي
- محتوى مناسب للموضوع
- تصميم متجاوب
- عناصر تفاعلية إذا لزم الأمر`;
                break;

            case 'css':
                prompt += `قم بإنشاء ملف CSS احترافي يتضمن:
- تصميم حديث وجذاب
- كود منظم ومعلق
- متغيرات CSS
- تصميم متجاوب
- تأثيرات وانتقالات سلسة`;
                break;

            case 'json':
                prompt += `قم بإنشاء بيانات JSON منظمة تتضمن:
- هيكل منطقي ومنظم
- بيانات حقيقية ومفيدة
- تنسيق JSON صحيح
- معلومات شاملة حول الموضوع
- بيانات قابلة للاستخدام العملي`;
                break;

            default:
                prompt += `قم بإنشاء محتوى ${fileType} احترافي ومفصل يحقق الهدف المطلوب بدقة.`;
        }

        prompt += `

المهم جداً:
- يجب أن يكون المحتوى مخصص 100% للطلب المحدد
- لا تستخدم محتوى عام أو افتراضي
- ركز على التفاصيل المطلوبة تحديداً
- اجعل المحتوى عملي وقابل للاستخدام
- قدم المحتوى جاهز للاستخدام بدون أي تفسيرات إضافية

ابدأ المحتوى مباشرة:`;

        return prompt;
    }

    // استخراج الموضوع من الطلب
    extractTopicFromRequest(userMessage) {
        // إزالة الكلمات الشائعة
        const commonWords = ['اعمل', 'لي', 'ملف', 'عن', 'حول', 'في', 'من', 'إلى', 'على', 'كود', 'برنامج', 'تطبيق'];
        const words = userMessage.split(' ').filter(word =>
            word.length > 2 && !commonWords.includes(word.toLowerCase())
        );
        return words.join(' ') || 'موضوع عام';
    }

    // تحليل نية المستخدم
    analyzeUserIntent(lowerMessage) {
        if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) return 'إنشاء حاسبة';
        if (lowerMessage.includes('لعبة') || lowerMessage.includes('game')) return 'إنشاء لعبة';
        if (lowerMessage.includes('موقع') || lowerMessage.includes('website')) return 'إنشاء موقع';
        if (lowerMessage.includes('قاعدة بيانات') || lowerMessage.includes('database')) return 'إنشاء قاعدة بيانات';
        if (lowerMessage.includes('تقرير') || lowerMessage.includes('report')) return 'إنشاء تقرير';
        if (lowerMessage.includes('دليل') || lowerMessage.includes('guide')) return 'إنشاء دليل';
        if (lowerMessage.includes('شرح') || lowerMessage.includes('explain')) return 'شرح موضوع';
        return 'إنشاء محتوى مخصص';
    }

    // استخراج المتطلبات الخاصة
    extractSpecificRequirements(lowerMessage) {
        const requirements = [];
        if (lowerMessage.includes('احترافي')) requirements.push('تصميم احترافي');
        if (lowerMessage.includes('بسيط')) requirements.push('تصميم بسيط');
        if (lowerMessage.includes('متقدم')) requirements.push('ميزات متقدمة');
        if (lowerMessage.includes('تفاعلي')) requirements.push('عناصر تفاعلية');
        if (lowerMessage.includes('متجاوب')) requirements.push('تصميم متجاوب');
        if (lowerMessage.includes('أمان') || lowerMessage.includes('حماية')) requirements.push('ميزات أمان');
        return requirements;
    }

    // إنشاء محتوى مخصص كحل أخير
    generateCustomContentFallback(userMessage, fileType) {
        console.log('🔧 النظام الأصلي: إنشاء محتوى مخصص كحل أخير');

        const topic = this.extractTopicFromRequest(userMessage);
        const intent = this.analyzeUserIntent(userMessage.toLowerCase());

        switch(fileType) {
            case 'javascript':
                return this.generateCustomJavaScript(topic, userMessage, intent);
            case 'python':
                return this.generateCustomPython(topic, userMessage, intent);
            case 'html':
                return this.generateCustomHTML(topic, userMessage, intent);
            case 'css':
                return this.generateCustomCSS(topic, userMessage, intent);
            case 'json':
                return this.generateCustomJSON(topic, userMessage, intent);
            case 'pdf':
                return this.generateCustomPDFContent(topic, userMessage, intent);
            default:
                return this.generateCustomTextContent(topic, userMessage, intent);
        }
    }

    // إنشاء محتوى JavaScript مخصص
    generateCustomJavaScript(topic, userMessage, intent) {
        const timestamp = new Date().toLocaleString('ar-SA');

        if (intent === 'إنشاء حاسبة') {
            return `/**
 * ${topic} - حاسبة JavaScript مخصصة
 * تم إنشاؤه خصيصاً للطلب: ${userMessage}
 * التاريخ: ${timestamp}
 */

class ${topic.replace(/\s+/g, '')}Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
        this.precision = 10;
        console.log('تم تشغيل ${topic}');
    }

    // العمليات الأساسية
    add(a, b) {
        const result = parseFloat((a + b).toFixed(this.precision));
        this.history.push(\`\${a} + \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    subtract(a, b) {
        const result = parseFloat((a - b).toFixed(this.precision));
        this.history.push(\`\${a} - \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    multiply(a, b) {
        const result = parseFloat((a * b).toFixed(this.precision));
        this.history.push(\`\${a} × \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    divide(a, b) {
        if (b === 0) {
            throw new Error('لا يمكن القسمة على صفر');
        }
        const result = parseFloat((a / b).toFixed(this.precision));
        this.history.push(\`\${a} ÷ \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    // عمليات متقدمة
    power(base, exponent) {
        const result = parseFloat(Math.pow(base, exponent).toFixed(this.precision));
        this.history.push(\`\${base}^\${exponent} = \${result}\`);
        this.result = result;
        return result;
    }

    sqrt(number) {
        if (number < 0) {
            throw new Error('لا يمكن حساب الجذر التربيعي لرقم سالب');
        }
        const result = parseFloat(Math.sqrt(number).toFixed(this.precision));
        this.history.push(\`√\${number} = \${result}\`);
        this.result = result;
        return result;
    }

    // إدارة التاريخ
    getHistory() {
        return this.history;
    }

    clearHistory() {
        this.history = [];
        console.log('تم مسح تاريخ العمليات');
    }

    getLastResult() {
        return this.result;
    }
}

// مثال على الاستخدام
const calculator = new ${topic.replace(/\s+/g, '')}Calculator();

// تجربة العمليات
console.log('مثال على الاستخدام:');
console.log('الجمع:', calculator.add(10, 5));
console.log('الضرب:', calculator.multiply(3, 4));
console.log('القوة:', calculator.power(2, 3));
console.log('التاريخ:', calculator.getHistory());

// تصدير الكلاس للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${topic.replace(/\s+/g, '')}Calculator;
}`;
        } else {
            return `/**
 * ${topic} - تطبيق JavaScript مخصص
 * تم إنشاؤه خصيصاً للطلب: ${userMessage}
 * التاريخ: ${timestamp}
 */

class ${topic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${topic}';
        this.version = '1.0.0';
        this.isRunning = false;
        this.data = {};
        this.init();
    }

    init() {
        console.log(\`تم تشغيل \${this.name}\`);
        this.setupEventListeners();
        this.loadData();
    }

    setupEventListeners() {
        // إعداد مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            this.onDOMReady();
        });
    }

    onDOMReady() {
        console.log('تم تحميل الصفحة بنجاح');
        this.start();
    }

    start() {
        this.isRunning = true;
        console.log(\`تم بدء تشغيل \${this.name}\`);
        this.mainLoop();
    }

    mainLoop() {
        if (!this.isRunning) return;

        // الحلقة الرئيسية للتطبيق
        this.processData();
        this.updateUI();

        // استمرار التشغيل
        setTimeout(() => this.mainLoop(), 1000);
    }

    processData() {
        // معالجة البيانات
        this.data.timestamp = new Date().toISOString();
        this.data.status = 'running';
    }

    updateUI() {
        // تحديث واجهة المستخدم
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = \`\${this.name} يعمل بنجاح\`;
        }
    }

    loadData() {
        // تحميل البيانات
        this.data = {
            name: this.name,
            version: this.version,
            created: new Date().toISOString(),
            description: 'تطبيق مخصص تم إنشاؤه حسب الطلب'
        };
    }

    stop() {
        this.isRunning = false;
        console.log(\`تم إيقاف \${this.name}\`);
    }

    getInfo() {
        return {
            name: this.name,
            version: this.version,
            isRunning: this.isRunning,
            data: this.data
        };
    }
}

// تشغيل التطبيق
const app = new ${topic.replace(/\s+/g, '')}App();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${topic.replace(/\s+/g, '')}App;
}`;
        }
    }

    // إنشاء محتوى أساسي كحل نهائي
    generateBasicCustomContent(userMessage, fileType) {
        const topic = this.extractTopicFromRequest(userMessage);
        const timestamp = new Date().toLocaleString('ar-SA');

        return `تم إنشاء هذا الملف خصيصاً للطلب: "${userMessage}"

الموضوع: ${topic}
نوع الملف: ${fileType}
التاريخ: ${timestamp}

هذا محتوى مخصص تم إنشاؤه حسب طلبك المحدد.
يمكنك تعديل هذا المحتوى حسب احتياجاتك.

تم إنشاء هذا الملف بواسطة النظام الأصلي للمساعد التقني الذكي.`;
    }

    // إنشاء محتوى HTML ذكي
    generateSmartHTMLContent(realTopic, userMessage, smartContent) {
        console.log('🌐 إنشاء محتوى HTML ذكي للموضوع:', realTopic);

        // محاولة توليد محتوى ذكي باستخدام النماذج المتاحة
        let enhancedContent = smartContent;
        let aiGeneratedContent = null;

        try {
            const htmlPrompt = `أنت خبير في إنشاء محتوى HTML احترافي. المستخدم يطلب: "${userMessage}"

الموضوع: ${realTopic}

قم بإنشاء محتوى تفصيلي ومفيد عن هذا الموضوع يتضمن:
- مقدمة شاملة عن الموضوع
- النقاط الرئيسية مرتبة ومنظمة
- معلومات دقيقة وحديثة
- أمثلة عملية إذا كان ذلك مناسباً
- خلاصة مفيدة

يجب أن يكون المحتوى مخصص 100% للموضوع المطلوب وليس محتوى عام.`;

            // محاولة استخدام النماذج المتاحة لتوليد محتوى ذكي
            console.log('🔗 البحث عن نماذج AI متاحة...');

            // جرب OpenRouter أولاً
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 محاولة استخدام OpenRouter...');
                try {
                    const response = await window.openRouterIntegration.smartSendMessage(htmlPrompt, {
                        mode: 'content_generator',
                        temperature: 0.7,
                        maxTokens: 2000
                    });
                    if (response && response.text && response.text.trim()) {
                        aiGeneratedContent = response.text.trim();
                        console.log('✅ تم توليد محتوى من OpenRouter');
                    }
                } catch (error) {
                    console.warn('⚠️ فشل OpenRouter:', error);
                }
            }

            // جرب النموذج المحلي
            if (!aiGeneratedContent && typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 محاولة استخدام النموذج المحلي...');
                try {
                    const localResponse = await window.technicalAssistant.getResponse(htmlPrompt);
                    if (localResponse && localResponse.trim()) {
                        aiGeneratedContent = localResponse.trim();
                        console.log('✅ تم توليد محتوى من النموذج المحلي');
                    }
                } catch (error) {
                    console.warn('⚠️ فشل النموذج المحلي:', error);
                }
            }

            // استخدام المحتوى المحسن
            if (aiGeneratedContent) {
                enhancedContent = {
                    title: realTopic,
                    content: aiGeneratedContent,
                    textContent: aiGeneratedContent
                };
                console.log('✅ تم استخدام محتوى ذكي مولد بواسطة AI');
            } else {
                console.log('📝 استخدام المحتوى الافتراضي المحسن');
                enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
            }

            // إذا تم توليد محتوى ذكي، استخدمه
            if (aiGeneratedContent) {
                enhancedContent = {
                    title: realTopic,
                    content: aiGeneratedContent,
                    textContent: aiGeneratedContent
                };
                console.log('✅ تم استخدام محتوى ذكي مولد بواسطة AI');
            } else {
                console.log('⚠️ لم يتم العثور على نماذج AI متاحة، استخدام المحتوى الافتراضي المحسن');
                // تحسين المحتوى الافتراضي
                enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
            }

        } catch (error) {
            console.warn('⚠️ خطأ في توليد محتوى ذكي للHTML:', error);
            enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
        }

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${realTopic}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; line-height: 1.6; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }
        h1 { color: #333; text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 15px; }
        h2 { color: #0056b3; margin-top: 30px; }
        h3 { color: #495057; margin-top: 25px; }
        ul { padding-right: 20px; }
        li { margin-bottom: 8px; }
        .meta { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; font-size: 0.9em; color: #666; }
        strong { color: #007bff; }
        .ai-badge { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; display: inline-block; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${enhancedContent.title}</h1>
        <div class="meta">
            ${aiGeneratedContent ? '<div class="ai-badge">🤖 محتوى مولد بواسطة الذكاء الاصطناعي</div><br>' : '<div style="background: #e3f2fd; color: #1976d2; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; display: inline-block; margin-bottom: 10px;">📝 محتوى محسن ومخصص</div><br>'}
            <strong>تم الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
            <strong>الطلب الأصلي:</strong> ${userMessage}
        </div>
        <div class="content">
            ${this.formatContentForHTML(enhancedContent.content || enhancedContent.textContent)}
        </div>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
        <p style="text-align: center; color: #6c757d; font-size: 0.9em;">
            تم إنشاء هذا المستند بواسطة المساعد الذكي
        </p>
    </div>
</body>
</html>`;
    }

    // تنسيق المحتوى للHTML
    formatContentForHTML(content) {
        if (!content) return '<p>محتوى افتراضي تم إنشاؤه بواسطة المساعد الذكي.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>');

        return formattedContent;
    }

    // إنشاء محتوى افتراضي محسن عندما لا تتوفر نماذج AI
    generateEnhancedDefaultContent(realTopic, userMessage) {
        console.log('📝 إنشاء محتوى افتراضي محسن للموضوع:', realTopic);

        // تحليل الموضوع لإنشاء محتوى مناسب
        const lowerTopic = realTopic.toLowerCase();
        let content = '';

        if (lowerTopic.includes('أمن') || lowerTopic.includes('حماية') || lowerTopic.includes('سيبراني')) {
            content = `
<h2>🔒 مقدمة عن ${realTopic}</h2>
<p>يعتبر ${realTopic} من أهم المواضيع في العصر الرقمي الحالي، حيث يلعب دوراً حيوياً في حماية المعلومات والأنظمة.</p>

<h2>📋 النقاط الرئيسية</h2>
<ul>
    <li><strong>التعريف:</strong> فهم المفاهيم الأساسية لـ ${realTopic}</li>
    <li><strong>الأهمية:</strong> لماذا يعتبر هذا الموضوع مهماً في الوقت الحالي</li>
    <li><strong>التطبيقات:</strong> كيفية تطبيق مبادئ ${realTopic} في الواقع</li>
    <li><strong>التحديات:</strong> العقبات والصعوبات المرتبطة بـ ${realTopic}</li>
</ul>

<h2>💡 أفضل الممارسات</h2>
<p>للحصول على أفضل النتائج في ${realTopic}، يُنصح باتباع المعايير المعترف بها دولياً والاستفادة من أحدث التقنيات المتاحة.</p>

<h2>🔮 المستقبل</h2>
<p>يتطور مجال ${realTopic} باستمرار، ومن المتوقع أن نشهد تطورات مهمة في السنوات القادمة تؤثر على كيفية تعاملنا مع هذا الموضوع.</p>
            `;
        } else if (lowerTopic.includes('برمجة') || lowerTopic.includes('تطوير') || lowerTopic.includes('كود')) {
            content = `
<h2>💻 مقدمة عن ${realTopic}</h2>
<p>${realTopic} هو مجال تقني متطور يتطلب فهماً عميقاً للمفاهيم الأساسية والتطبيقات العملية.</p>

<h2>🛠️ الأدوات والتقنيات</h2>
<ul>
    <li><strong>اللغات:</strong> أهم لغات البرمجة المستخدمة في ${realTopic}</li>
    <li><strong>الأدوات:</strong> بيئات التطوير والأدوات المساعدة</li>
    <li><strong>المكتبات:</strong> أهم المكتبات والإطارات المستخدمة</li>
    <li><strong>المنصات:</strong> منصات النشر والاستضافة</li>
</ul>

<h2>📚 التعلم والتطوير</h2>
<p>لإتقان ${realTopic}، يُنصح بالممارسة المستمرة والاطلاع على أحدث التطورات في هذا المجال.</p>

<h2>🚀 المشاريع العملية</h2>
<p>أفضل طريقة لتعلم ${realTopic} هي من خلال العمل على مشاريع حقيقية تطبق المفاهيم النظرية.</p>
            `;
        } else {
            content = `
<h2>📖 مقدمة عن ${realTopic}</h2>
<p>يعتبر ${realTopic} موضوعاً مهماً يستحق الدراسة والفهم العميق. هذا المستند يقدم نظرة شاملة حول هذا الموضوع.</p>

<h2>🎯 الأهداف الرئيسية</h2>
<ul>
    <li>فهم المفاهيم الأساسية لـ ${realTopic}</li>
    <li>التعرف على التطبيقات العملية</li>
    <li>استكشاف الفرص والتحديات</li>
    <li>وضع خطة للتطوير والتحسين</li>
</ul>

<h2>📊 التحليل والدراسة</h2>
<p>من خلال دراسة ${realTopic} بعمق، يمكننا الوصول إلى فهم أفضل للموضوع وتطبيقاته المختلفة.</p>

<h2>💼 التطبيقات العملية</h2>
<p>يمكن تطبيق مبادئ ${realTopic} في مجالات متعددة، مما يجعله موضوعاً ذا قيمة عملية عالية.</p>

<h2>🔍 الخلاصة</h2>
<p>في الختام، ${realTopic} موضوع غني بالمعلومات والتطبيقات، ويستحق المزيد من البحث والدراسة.</p>
            `;
        }

        return {
            title: realTopic,
            content: content,
            textContent: content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
        };
    }

    // إنشاء محتوى CSS ذكي
    generateSmartCSSContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('موقع') || lowerMessage.includes('صفحة ويب')) {
            return `/* ${realTopic} - تصميم موقع احترافي */
/* تم إنشاؤه بواسطة المساعد التقني الذكي */

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* تصميم الجسم الرئيسي */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* تصميم الرأس */
header {
    text-align: center;
    padding: 40px 0;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 40px;
}

h1 {
    font-size: 3em;
    color: #2c3e50;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* تصميم الأزرار */
.btn {
    display: inline-block;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    h1 {
        font-size: 2em;
    }
}`;
        } else {
            return `/* ${realTopic} - تصميم CSS مخصص */
/* تم إنشاؤه بواسطة المساعد التقني الذكي */

/* متغيرات CSS للألوان */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* تصميم العناوين */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* تصميم الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}`;
        }
    }

    // إنشاء محتوى XML ذكي
    generateSmartXMLContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${realTopic} - ملف بيانات الأمن السيبراني -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->

<cybersecurity>
    <metadata>
        <title>الأمن السيبراني</title>
        <version>1.0</version>
        <created>${new Date().toISOString()}</created>
        <language>ar</language>
    </metadata>

    <security_principles>
        <principle id="1">
            <name>السرية</name>
            <description>حماية المعلومات من الوصول غير المصرح</description>
            <importance>عالي</importance>
        </principle>
        <principle id="2">
            <name>التكامل</name>
            <description>ضمان دقة وسلامة البيانات</description>
            <importance>عالي</importance>
        </principle>
    </security_principles>

    <threats>
        <threat id="1">
            <name>البرمجيات الخبيثة</name>
            <type>malware</type>
            <severity>عالي</severity>
            <prevention>برامج مكافحة الفيروسات</prevention>
        </threat>
    </threats>
</cybersecurity>`;
        } else {
            return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${realTopic} - ملف بيانات XML -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->

<document>
    <metadata>
        <title>${realTopic}</title>
        <created>${new Date().toISOString()}</created>
        <creator>المساعد التقني الذكي</creator>
        <language>ar</language>
    </metadata>

    <content>
        <section id="1">
            <title>مقدمة</title>
            <description>مقدمة شاملة حول ${realTopic}</description>
            <items>
                <item id="1">
                    <name>النقطة الأولى</name>
                    <value>معلومات مهمة حول ${realTopic}</value>
                </item>
            </items>
        </section>
    </content>
</document>`;
        }
    }

    // إظهار تأكيد التفعيل مع تشخيص شامل
    showActivationConfirmation() {
        console.log('🔧 النظام الأصلي: بدء التشخيص الشامل...');

        // فحص المكونات الأساسية
        const diagnostics = {
            jsPDF: typeof window.jsPDF !== 'undefined',
            openRouter: window.openRouterIntegration && window.openRouterIntegration.isEnabled,
            localAI: typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse,
            otherAI: typeof window.getAIModelResponse === 'function' || typeof window.generateAIContent === 'function',
            downloadContainer: typeof this.createProfessionalDownloadContainer === 'function'
        };

        console.log('📊 النظام الأصلي - تشخيص المكونات:', diagnostics);

        const availableFeatures = [];
        if (diagnostics.jsPDF) availableFeatures.push('PDF حقيقي');
        if (diagnostics.openRouter) availableFeatures.push('OpenRouter');
        if (diagnostics.localAI) availableFeatures.push('AI محلي');
        if (diagnostics.otherAI) availableFeatures.push('AI إضافي');

        // إنشاء إشعار مرئي
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white; padding: 15px 25px; border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            font-weight: bold; font-size: 16px;
            animation: slideIn 0.5s ease-out;
        `;
        notification.innerHTML = `
            <div>📁 تم تفعيل النظام الأصلي بنجاح!</div>
            <div style="font-size: 12px; opacity: 0.9; margin-top: 5px;">
                الميزات: ${availableFeatures.join(', ') || 'أساسي'}
            </div>
        `;

        // إضافة CSS للحركة
        if (!document.getElementById('fileCreatorStyles')) {
            const style = document.createElement('style');
            style.id = 'fileCreatorStyles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Handle PDF creation with AI
    async handlePDFCreation(command) {
        const topic = this.extractTopic(command);
        
        const pdfPrompt = `أنت خبير في إنشاء المستندات الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء محتوى PDF احترافي شامل يتضمن:

1. **العنوان الرئيسي والفهرس**
2. **مقدمة شاملة**
3. **المحتوى الأساسي** (مقسم لأقسام منطقية)
4. **الصور والرسوم البيانية المطلوبة** (اذكر أنواعها)
5. **الخلاصة والتوصيات**
6. **المراجع والمصادر**

اجعل المحتوى:
- احترافي ومفصل
- منظم ومنسق
- يحتوي على معلومات حديثة ودقيقة
- مناسب للطباعة والعرض

قدم المحتوى بتنسيق جاهز للتحويل إلى PDF.`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PDF...');
                const response = await window.openRouterIntegration.smartSendMessage(pdfPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PDF...');
                const response = await window.huggingFaceManager.sendMessage(pdfPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PDF...');
                content = await technicalAssistant.getResponse(pdfPrompt);
            }

            if (content) {
                // Create PDF file
                await this.createPDFFile(topic, content);

                return `✅ **تم إنشاء ملف PDF بنجاح!**

📄 **الملف:** ${topic}.pdf
📊 **المحتوى:** ${content.length} حرف من المحتوى الاحترافي
🎨 **التنسيق:** تنسيق نصي منظم ومنسق
💡 **ملاحظة:** ملف نصي بامتداد PDF يفتح في أي محرر نصوص

تم حفظ الملف وهو جاهز للتحميل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء محتوى احترافي.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PDF: ${error.message}`;
        }
    }

    // Handle PowerPoint creation with AI
    async handlePowerPointCreation(command) {
        const topic = this.extractTopic(command);
        
        const pptPrompt = `أنت خبير في إنشاء العروض التقديمية الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء عرض PowerPoint احترافي يتضمن:

1. **شريحة العنوان** - عنوان جذاب ومعلومات المقدم
2. **شريحة الفهرس** - نظرة عامة على المحتوى
3. **شرائح المحتوى الأساسي** (8-12 شريحة)
4. **شرائح الرسوم البيانية والإحصائيات**
5. **شريحة الخلاصة والتوصيات**
6. **شريحة الأسئلة والمناقشة**

لكل شريحة قدم:
- العنوان الرئيسي
- النقاط الأساسية (3-5 نقاط)
- اقتراحات للصور والرسوم
- ملاحظات للمقدم

اجعل العرض:
- جذاب ومتفاعل
- مناسب للجمهور المستهدف
- يحتوي على معلومات قيمة ومحدثة
- سهل الفهم والمتابعة`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PowerPoint...');
                const response = await window.openRouterIntegration.smartSendMessage(pptPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PowerPoint...');
                const response = await window.huggingFaceManager.sendMessage(pptPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PowerPoint...');
                content = await technicalAssistant.getResponse(pptPrompt);
            }

            if (content) {
                // Create PowerPoint file
                await this.createPowerPointFile(topic, content);

                return `✅ **تم إنشاء عرض PowerPoint بنجاح!**

🎯 **الملف:** ${topic}.pptx
📊 **المحتوى:** عرض احترافي متكامل
🎨 **التصميم:** تصميم حديث وجذاب
📸 **الصور:** صور ورسوم بيانية مناسبة

تم حفظ العرض وهو جاهز للاستخدام! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء عروض احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PowerPoint: ${error.message}`;
        }
    }

    // Handle EXE creation with AI
    async handleEXECreation(command) {
        const programType = this.extractProgramType(command);
        
        const exePrompt = `أنت خبير في تطوير البرامج والتطبيقات. المستخدم يطلب: "${command}"

نوع البرنامج: ${programType}

قم بإنشاء برنامج EXE احترافي يتضمن:

1. **الكود المصدري الكامل** (Python/C++/C#)
2. **واجهة المستخدم الرسومية** (GUI)
3. **الوظائف الأساسية والمتقدمة**
4. **معالجة الأخطاء والاستثناءات**
5. **ملف التعليمات والمساعدة**
6. **أيقونة البرنامج والموارد**

اجعل البرنامج:
- سهل الاستخدام ومفهوم
- يحتوي على جميع الوظائف المطلوبة
- محمي من الأخطاء
- قابل للتوزيع والتشغيل

قدم الكود الكامل مع تعليمات التجميع.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const code = await technicalAssistant.getResponse(exePrompt);
                
                // Create EXE file
                await this.createEXEFile(programType, code);
                
                return `✅ **تم إنشاء برنامج EXE بنجاح!**

💻 **الملف:** ${programType}.exe
🔧 **الوظائف:** جميع الوظائف المطلوبة
🎨 **الواجهة:** واجهة رسومية احترافية
📁 **الحجم:** محسن للأداء

تم إنشاء البرنامج وهو جاهز للتشغيل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء برامج احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء EXE: ${error.message}`;
        }
    }

    // Extract topic from command
    extractTopic(command) {
        // Remove common words and extract main topic
        const cleanCommand = command
            .replace(/أنشئ|اعمل|pdf|powerpoint|عرض|تقرير|عن|حول|في/gi, '')
            .trim();

        return cleanCommand || 'موضوع عام';
    }

    // استخراج الموضوع الحقيقي من الطلب
    extractRealTopic(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // البحث عن أنماط مختلفة للموضوع
        let topic = '';

        // نمط "عن [الموضوع]"
        const aboutMatch = userMessage.match(/عن\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
        if (aboutMatch) {
            topic = aboutMatch[1].trim();
        }

        // نمط "حول [الموضوع]"
        if (!topic) {
            const aroundMatch = userMessage.match(/حول\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
            if (aroundMatch) {
                topic = aroundMatch[1].trim();
            }
        }

        // نمط "ملف [نوع] [الموضوع]"
        if (!topic) {
            const fileMatch = userMessage.match(/ملف\s+\w+\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
            if (fileMatch) {
                topic = fileMatch[1].trim();
            }
        }

        // إزالة الكلمات الشائعة
        if (topic) {
            topic = topic.replace(/\b(بشكل|عام|شامل|مفصل|كامل|جاهز|للتحميل|وقم|بوضع|الشرح)\b/gi, '').trim();
        }

        // إذا لم نجد موضوع محدد، استخدم الطريقة القديمة
        if (!topic) {
            topic = this.extractTopic(userMessage);
        }

        console.log('🎯 الموضوع المستخرج:', topic);
        return topic || 'موضوع عام';
    }

    // إنشاء محتوى ذكي حسب الموضوع
    generateSmartContentByTopic(topic, userMessage) {
        const lowerTopic = topic.toLowerCase();
        const lowerMessage = userMessage.toLowerCase();

        // مواضيع الأمن السيبراني
        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية') ||
            lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return this.generateSecurityContent();
        }

        // مواضيع الذكاء الاصطناعي
        if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم') ||
            lowerMessage.includes('ذكاء') || lowerMessage.includes('ai')) {
            return this.generateAIContent();
        }

        // مواضيع البرمجة
        if (lowerTopic.includes('برمجة') || lowerTopic.includes('كود') || lowerTopic.includes('تطوير') ||
            lowerMessage.includes('برمجة') || lowerMessage.includes('كود')) {
            return this.generateProgrammingContent();
        }

        // مواضيع التسويق
        if (lowerTopic.includes('تسويق') || lowerTopic.includes('إعلان') || lowerTopic.includes('مبيعات')) {
            return this.generateMarketingContent();
        }

        // مواضيع التعليم
        if (lowerTopic.includes('تعليم') || lowerTopic.includes('دراسة') || lowerTopic.includes('تدريب')) {
            return this.generateEducationContent();
        }

        // محتوى عام حسب الموضوع
        return this.generateGeneralContent(topic);
    }

    // محتوى الأمن السيبراني
    generateSecurityContent() {
        return {
            title: 'الأمن السيبراني',
            content: `
                <h2>🔒 الأمن السيبراني - دليل شامل</h2>

                <h3>📋 تعريف الأمن السيبراني</h3>
                <p>الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية. تهدف هذه الهجمات عادة إلى الوصول إلى المعلومات الحساسة أو تغييرها أو تدميرها.</p>

                <h3>🛡️ المبادئ الأساسية</h3>
                <ul>
                    <li><strong>السرية (Confidentiality):</strong> ضمان وصول المعلومات للأشخاص المخولين فقط</li>
                    <li><strong>التكامل (Integrity):</strong> ضمان دقة وكمال المعلومات</li>
                    <li><strong>التوفر (Availability):</strong> ضمان توفر المعلومات عند الحاجة</li>
                </ul>

                <h3>⚠️ أنواع التهديدات</h3>
                <ul>
                    <li>البرمجيات الخبيثة (Malware)</li>
                    <li>هجمات التصيد (Phishing)</li>
                    <li>هجمات الحرمان من الخدمة (DDoS)</li>
                    <li>اختراق كلمات المرور</li>
                    <li>الهندسة الاجتماعية</li>
                </ul>

                <h3>🔧 أدوات الحماية</h3>
                <ul>
                    <li>جدران الحماية (Firewalls)</li>
                    <li>برامج مكافحة الفيروسات</li>
                    <li>التشفير (Encryption)</li>
                    <li>المصادقة متعددة العوامل (MFA)</li>
                    <li>أنظمة كشف التطفل (IDS/IPS)</li>
                </ul>

                <h3>💡 أفضل الممارسات</h3>
                <ul>
                    <li>استخدام كلمات مرور قوية ومعقدة</li>
                    <li>تحديث البرامج والأنظمة بانتظام</li>
                    <li>عمل نسخ احتياطية منتظمة</li>
                    <li>تدريب الموظفين على الوعي الأمني</li>
                    <li>مراقبة الشبكة والأنظمة باستمرار</li>
                </ul>`,
            textContent: `🔒 الأمن السيبراني - دليل شامل

📋 تعريف الأمن السيبراني:
الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية.

🛡️ المبادئ الأساسية:
• السرية (Confidentiality): ضمان وصول المعلومات للأشخاص المخولين فقط
• التكامل (Integrity): ضمان دقة وكمال المعلومات
• التوفر (Availability): ضمان توفر المعلومات عند الحاجة

⚠️ أنواع التهديدات:
• البرمجيات الخبيثة (Malware)
• هجمات التصيد (Phishing)
• هجمات الحرمان من الخدمة (DDoS)
• اختراق كلمات المرور
• الهندسة الاجتماعية

🔧 أدوات الحماية:
• جدران الحماية (Firewalls)
• برامج مكافحة الفيروسات
• التشفير (Encryption)
• المصادقة متعددة العوامل (MFA)
• أنظمة كشف التطفل (IDS/IPS)

💡 أفضل الممارسات:
• استخدام كلمات مرور قوية ومعقدة
• تحديث البرامج والأنظمة بانتظام
• عمل نسخ احتياطية منتظمة
• تدريب الموظفين على الوعي الأمني
• مراقبة الشبكة والأنظمة باستمرار`
        };
    }

    // Extract program type from command
    extractProgramType(command) {
        if (command.includes('حاسبة') || command.includes('calculator')) return 'آلة حاسبة';
        if (command.includes('محول') || command.includes('converter')) return 'محول ملفات';
        if (command.includes('منظم') || command.includes('organizer')) return 'منظم ملفات';
        if (command.includes('لعبة') || command.includes('game')) return 'لعبة بسيطة';
        
        return 'أداة مساعدة';
    }

    // Create PDF file as text document
    async createPDFFile(topic, content) {
        try {
            console.log('📄 إنشاء ملف PDF (نصي)...');

            // إنشاء محتوى نصي منسق
            const realTopic = this.extractRealTopic(`عن ${topic}`);
            const smartContent = this.generateSmartContentByTopic(realTopic, `عن ${topic}`);

            const pdfTextContent = `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
===============================================`;

            // إنشاء PDF حقيقي أولاً باستخدام jsPDF
            try {
                const realPDFBlob = await this.createRealPDFContent(smartContent, `عن ${topic}`, topic);
                if (realPDFBlob instanceof Blob && realPDFBlob.size > 1000) {
                    const url = URL.createObjectURL(realPDFBlob);
                    console.log('📄 إنشاء PDF حقيقي بنجاح:', `${topic}.pdf`, realPDFBlob.size, 'بايت');
                    this.createProfessionalDownloadContainer(`${topic}.pdf`, url, realPDFBlob.size);
                    return;
                }
            } catch (error) {
                console.warn('⚠️ فشل في إنشاء PDF حقيقي، استخدام النظام البديل:', error);
            }

            // النظام البديل - ملف HTML بامتداد PDF (يفتح في المتصفح)
            const htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .content { margin: 20px 0; white-space: pre-wrap; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; color: #666; }
    </style>
</head>
<body>
    <h1>${smartContent.title}</h1>
    <div class="content">${smartContent.textContent}</div>
    <div class="footer">
        <p>تم إنشاء هذا المستند بواسطة المساعد التقني الذكي</p>
        <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
</body>
</html>`;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            console.log('📄 إنشاء ملف PDF (نصي):', `${topic}.pdf`);

            // استدعاء الحاوية الاحترافية مباشرة
            this.createProfessionalDownloadContainer(`${topic}.pdf`, url, blob.size);

            // Add to creation history
            this.creationHistory.push({
                type: 'PDF',
                name: `${topic}.pdf`,
                created: new Date(),
                size: pdfTextContent.length
            });

            console.log('✅ تم إنشاء ملف PDF بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF:', error);
            throw error;
        }
    }

    // Create PowerPoint file with real download
    async createPowerPointFile(topic, content) {
        try {
            console.log('🎯 إنشاء عرض PowerPoint احترافي...');

            // Create PowerPoint-like HTML presentation
            const slides = this.parseContentToSlides(content);
            const pptHTML = this.generatePowerPointHTML(topic, slides);

            // إنشاء الملف وتحميله
            const success = this.createDownloadableFile(pptHTML, `${topic}_presentation.html`, 'text/html');

            if (success) {
                // إنشاء ملف نصي للمحتوى أيضاً
                const textContent = this.generatePresentationText(topic, slides);
                this.createDownloadableFile(textContent, `${topic}_notes.txt`, 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'PowerPoint',
                    name: `${topic}_presentation.html`,
                    created: new Date(),
                    slides: slides.length
                });

                console.log('✅ تم إنشاء عرض PowerPoint بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('PowerPoint', `${topic}_presentation.html`, slides.length);
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء PowerPoint:', error);
            throw error;
        }
    }

    // Create EXE file (Python script with compilation instructions)
    async createEXEFile(programType, code) {
        try {
            console.log('💻 إنشاء برنامج EXE...');

            // Create Python script
            const pythonCode = this.generatePythonCode(programType, code);

            // إنشاء ملف Python وتحميله
            const pythonSuccess = this.createDownloadableFile(pythonCode, `${programType}.py`, 'text/x-python');

            if (pythonSuccess) {
                // Create batch file for compilation
                const batchScript = this.generateCompilationScript(programType);
                this.createDownloadableFile(batchScript, 'compile_to_exe.bat', 'text/plain');

                // Create README file
                const readmeContent = this.generateReadmeFile(programType);
                this.createDownloadableFile(readmeContent, 'README.txt', 'text/plain');

                // Create requirements file
                const requirements = this.generateRequirementsFile(programType);
                this.createDownloadableFile(requirements, 'requirements.txt', 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'EXE Package',
                    name: `${programType}.py`,
                    created: new Date(),
                    files: 4
                });

                console.log('✅ تم إنشاء حزمة EXE بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('EXE Package', `${programType}.py`, '4 ملفات');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء EXE:', error);
            throw error;
        }
    }

    // إنشاء PDF حقيقي باستخدام jsPDF
    async createRealPDFContent(smartContent, userMessage, topic) {
        try {
            console.log('📄 محاولة إنشاء PDF حقيقي باستخدام jsPDF...');

            // تحميل مكتبة jsPDF
            const jsPDF = await this.loadJsPDF();
            if (!jsPDF) {
                throw new Error('فشل في تحميل مكتبة jsPDF');
            }

            // إنشاء مستند PDF جديد
            const doc = new jsPDF.jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // إعداد الخط العربي (إذا كان متاحاً)
            try {
                doc.setFont('Arial', 'normal');
            } catch (e) {
                console.warn('⚠️ الخط العربي غير متاح، استخدام الخط الافتراضي');
            }

            // إضافة العنوان
            doc.setFontSize(20);
            doc.text(smartContent.title || topic, 105, 30, { align: 'center' });

            // إضافة خط تحت العنوان
            doc.setLineWidth(0.5);
            doc.line(20, 35, 190, 35);

            // إضافة المحتوى
            doc.setFontSize(12);
            let yPosition = 50;

            if (smartContent.textContent) {
                const content = smartContent.textContent.replace(/[^\x00-\x7F]/g, '?'); // تحويل الأحرف العربية
                const lines = doc.splitTextToSize(content, 170);

                // إضافة النص مع التحكم في المسافات
                lines.forEach((line) => {
                    if (yPosition > 270) { // إضافة صفحة جديدة إذا لزم الأمر
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(line, 20, yPosition);
                    yPosition += 7;
                });
            }

            // إضافة تذييل
            const pageCount = doc.internal.getNumberOfPages();
            for (let i = 1; i <= pageCount; i++) {
                doc.setPage(i);
                doc.setFontSize(10);
                doc.text(`Page ${i} of ${pageCount}`, 105, 285, { align: 'center' });
                doc.text(`Created by AI Assistant - ${new Date().toLocaleDateString()}`, 105, 290, { align: 'center' });
            }

            // تحويل إلى Blob
            const pdfBlob = doc.output('blob');
            console.log('✅ تم إنشاء PDF حقيقي بنجاح:', pdfBlob.size, 'بايت');

            return pdfBlob;

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF حقيقي:', error);
            throw error;
        }
    }

    // Load jsPDF library dynamically
    async loadJsPDF() {
        return new Promise((resolve, reject) => {
            if (window.jspdf) {
                resolve(window.jspdf);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل مكتبة jsPDF بنجاح');
                resolve(window.jspdf);
            };
            script.onerror = (error) => {
                console.error('❌ فشل في تحميل مكتبة jsPDF:', error);
                reject(error);
            };
            document.head.appendChild(script);
        });
    }

    // Parse content to slides
    parseContentToSlides(content) {
        const sections = content.split(/\n\s*\n/);
        return sections.map((section, index) => ({
            title: `شريحة ${index + 1}`,
            content: section.trim()
        }));
    }

    // Generate PowerPoint HTML
    generatePowerPointHTML(topic, slides) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - عرض تقديمي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f0f0f0; }
        .presentation { max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .slide { padding: 60px; min-height: 500px; border-bottom: 2px solid #eee; page-break-after: always; }
        .slide h1 { color: #2c3e50; font-size: 2.5rem; margin-bottom: 30px; text-align: center; }
        .slide h2 { color: #34495e; font-size: 2rem; margin-bottom: 20px; }
        .slide p { font-size: 1.2rem; line-height: 1.6; margin-bottom: 15px; }
        .slide ul { font-size: 1.1rem; line-height: 1.8; }
        .title-slide { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; }
        .title-slide h1 { color: white; font-size: 3rem; }
        @media print { .slide { page-break-after: always; } }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide title-slide">
            <h1>${topic}</h1>
            <p style="font-size: 1.5rem; margin-top: 50px;">عرض تقديمي احترافي</p>
            <p style="font-size: 1.2rem;">تم إنشاؤه بواسطة المساعد التقني الذكي</p>
            <p style="font-size: 1rem; margin-top: 100px;">${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        ${slides.map(slide => `
        <div class="slide">
            <h2>${slide.title}</h2>
            <div>${slide.content.replace(/\n/g, '<br>')}</div>
        </div>
        `).join('')}
    </div>
</body>
</html>`;
    }

    // Generate Python code for EXE
    generatePythonCode(programType, aiCode) {
        const baseCode = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${programType}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

class ${programType.replace(/\s+/g, '')}App:
    def __init__(self, root):
        self.root = root
        self.root.title("${programType}")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # Set up the GUI
        self.setup_gui()

    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="${programType}", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Add AI-generated functionality here
        ${this.extractPythonFunctionality(aiCode)}

    def show_about(self):
        messagebox.showinfo("حول البرنامج",
                          "${programType}\\nتم إنشاؤه بواسطة المساعد التقني الذكي")

def main():
    root = tk.Tk()
    app = ${programType.replace(/\s+/g, '')}App(root)
    root.mainloop()

if __name__ == "__main__":
    main()
`;
        return baseCode;
    }

    // Extract Python functionality from AI code
    extractPythonFunctionality(aiCode) {
        // Simple extraction - in real implementation, this would be more sophisticated
        return `        # الوظائف الأساسية
        self.create_main_interface()

    def create_main_interface(self):
        # واجهة البرنامج الرئيسية
        pass`;
    }

    // Generate compilation script
    generateCompilationScript(programType) {
        return `@echo off
echo تجميع ${programType} إلى ملف EXE...
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install pyinstaller

REM تجميع البرنامج
echo تجميع البرنامج...
pyinstaller --onefile --windowed "${programType}.py"

echo.
echo تم تجميع البرنامج بنجاح!
echo ستجد ملف EXE في مجلد dist
pause`;
    }

    // Generate README file
    generateReadmeFile(programType) {
        return `${programType} - دليل الاستخدام

تم إنشاء هذا البرنامج بواسطة المساعد التقني الذكي

محتويات الحزمة:
- ${programType}.py: الكود المصدري للبرنامج
- compile.bat: ملف تجميع البرنامج إلى EXE
- README.txt: هذا الملف

طريقة التشغيل:
1. تأكد من تثبيت Python على النظام
2. شغل ملف compile.bat لتجميع البرنامج
3. ستجد ملف EXE في مجلد dist

أو يمكنك تشغيل البرنامج مباشرة:
python "${programType}.py"

للدعم والمساعدة:
استخدم المساعد التقني الذكي`;
    }

    // Create ZIP package
    async createZipPackage(name, files) {
        // Using JSZip library for creating ZIP files
        const JSZip = window.JSZip || await this.loadJSZip();
        const zip = new JSZip();

        // Add files to ZIP
        Object.entries(files).forEach(([filename, content]) => {
            zip.file(filename, content);
        });

        // Generate ZIP file
        const content = await zip.generateAsync({ type: 'blob' });

        // Download ZIP file
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}_package.zip`;
        a.click();

        URL.revokeObjectURL(url);
    }

    // Load JSZip library
    async loadJSZip() {
        return new Promise((resolve, reject) => {
            if (window.JSZip) {
                resolve(window.JSZip);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = () => resolve(window.JSZip);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Handle general file creation
    async handleGeneralFileCreation(command) {
        const fileType = this.detectFileType(command);

        switch (fileType) {
            case 'pdf':
                return await this.handlePDFCreation(command);
            case 'powerpoint':
                return await this.handlePowerPointCreation(command);
            case 'exe':
                return await this.handleEXECreation(command);
            case 'word':
                return await this.handleWordCreation(command);
            case 'excel':
                return await this.handleExcelCreation(command);
            case 'html':
                return await this.handleHTMLCreation(command);
            case 'css':
                return await this.handleHTMLCreation(command); // CSS كـ HTML مع تنسيق
            case 'javascript':
                return await this.handleHTMLCreation(command); // JS كـ HTML مع تنسيق
            case 'python':
                return await this.handleHTMLCreation(command); // Python كـ HTML مع تنسيق
            case 'java':
                return await this.handleHTMLCreation(command); // Java كـ HTML مع تنسيق
            case 'cpp':
                return await this.handleHTMLCreation(command); // C++ كـ HTML مع تنسيق
            case 'json':
                return await this.handleHTMLCreation(command); // JSON كـ HTML مع تنسيق
            case 'xml':
                return await this.handleHTMLCreation(command); // XML كـ HTML مع تنسيق
            case 'markdown':
                return await this.handleHTMLCreation(command); // Markdown كـ HTML مع تنسيق
            case 'text':
                return await this.handleHTMLCreation(command); // Text كـ HTML مع تنسيق
            default:
                return await this.handleHTMLCreation(command); // افتراضي HTML
        }
    }

    // Detect file type from command
    detectFileType(command) {
        const lowerCommand = command.toLowerCase();

        // ملفات المكتب
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) return 'pdf';
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) return 'powerpoint';
        if (lowerCommand.includes('word') || lowerCommand.includes('مستند')) return 'word';
        if (lowerCommand.includes('excel') || lowerCommand.includes('جدول')) return 'excel';

        // ملفات البرمجة والويب
        if (lowerCommand.includes('html') || lowerCommand.includes('صفحة') || lowerCommand.includes('موقع')) return 'html';
        if (lowerCommand.includes('css') || lowerCommand.includes('تنسيق') || lowerCommand.includes('ستايل')) return 'css';
        if (lowerCommand.includes('javascript') || lowerCommand.includes('js') || lowerCommand.includes('جافا سكريبت')) return 'javascript';
        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون') || lowerCommand.includes('.py')) return 'python';
        if (lowerCommand.includes('java') || lowerCommand.includes('جافا') || lowerCommand.includes('.java')) return 'java';
        if (lowerCommand.includes('cpp') || lowerCommand.includes('c++') || lowerCommand.includes('سي بلس')) return 'cpp';
        if (lowerCommand.includes('json') || lowerCommand.includes('بيانات') || lowerCommand.includes('api')) return 'json';
        if (lowerCommand.includes('xml') || lowerCommand.includes('markup')) return 'xml';
        if (lowerCommand.includes('markdown') || lowerCommand.includes('md') || lowerCommand.includes('توثيق')) return 'markdown';

        // ملفات أخرى
        if (lowerCommand.includes('txt') || lowerCommand.includes('نص') || lowerCommand.includes('text')) return 'text';
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج')) return 'exe';

        return 'html'; // افتراضي HTML بدلاً من general
    }

    // تحويل نوع الملف إلى معلومات التحميل
    getFileTypeInfo(detectedType) {
        const typeMap = {
            'html': { type: 'html', mimeType: 'text/html', extension: '.html' },
            'css': { type: 'css', mimeType: 'text/css', extension: '.css' },
            'javascript': { type: 'javascript', mimeType: 'text/javascript', extension: '.js' },
            'python': { type: 'python', mimeType: 'text/x-python', extension: '.py' },
            'java': { type: 'java', mimeType: 'text/x-java', extension: '.java' },
            'cpp': { type: 'cpp', mimeType: 'text/x-c++src', extension: '.cpp' },
            'csharp': { type: 'csharp', mimeType: 'text/x-csharp', extension: '.cs' },
            'php': { type: 'php', mimeType: 'text/x-php', extension: '.php' },
            'json': { type: 'json', mimeType: 'application/json', extension: '.json' },
            'xml': { type: 'xml', mimeType: 'application/xml', extension: '.xml' },
            'sql': { type: 'sql', mimeType: 'text/x-sql', extension: '.sql' },
            'yaml': { type: 'yaml', mimeType: 'text/yaml', extension: '.yml' },
            'shell': { type: 'shell', mimeType: 'text/x-shellscript', extension: '.sh' },
            'powershell': { type: 'powershell', mimeType: 'text/x-powershell', extension: '.ps1' },
            'markdown': { type: 'markdown', mimeType: 'text/markdown', extension: '.md' },
            'text': { type: 'text', mimeType: 'text/plain', extension: '.txt' },
            'pdf': { type: 'pdf', mimeType: 'text/html', extension: '.pdf' },
            'powerpoint': { type: 'powerpoint', mimeType: 'text/plain', extension: '.pptx' },
            'word': { type: 'word', mimeType: 'text/html', extension: '.docx' },
            'excel': { type: 'excel', mimeType: 'text/csv', extension: '.xlsx' },
            'exe': { type: 'exe', mimeType: 'text/plain', extension: '.exe' }
        };

        return typeMap[detectedType] || typeMap['html'];
    }

    // Handle internet integration
    async handleInternetIntegration(command) {
        if (!this.internetAccess) {
            return 'التكامل مع الإنترنت غير مفعل حالياً';
        }

        const internetIntegration = new InternetIntegration();

        if (command.includes('صور') || command.includes('images')) {
            const topic = this.extractTopic(command);
            const images = await internetIntegration.searchImages(topic);
            return `تم العثور على ${images.length} صورة عن "${topic}". سيتم استخدامها في الملف.`;
        }

        if (command.includes('بيانات') || command.includes('معلومات')) {
            const topic = this.extractTopic(command);
            const data = await internetIntegration.fetchRealTimeData(topic);
            return `تم جلب معلومات حديثة عن "${topic}" من الإنترنت.`;
        }

        return 'تم تفعيل التكامل مع الإنترنت لجلب المحتوى المطلوب.';
    }

    // تحديد نوع الملف الذكي من السياق
    detectSmartFileType(command) {
        const lowerCommand = command.toLowerCase();

        // فحص السياق لتحديد النوع الأنسب
        if (lowerCommand.includes('صفحة') || lowerCommand.includes('موقع') || lowerCommand.includes('تسجيل دخول') || lowerCommand.includes('نموذج')) {
            return 'صفحة HTML';
        }
        if (lowerCommand.includes('تقرير') || lowerCommand.includes('مستند') || lowerCommand.includes('وثيقة')) {
            return 'مستند HTML';
        }
        if (lowerCommand.includes('قائمة') || lowerCommand.includes('جدول') || lowerCommand.includes('بيانات')) {
            return 'جدول HTML';
        }
        if (lowerCommand.includes('سكريبت') || lowerCommand.includes('برنامج') || lowerCommand.includes('كود')) {
            return 'ملف برمجي';
        }

        return 'ملف HTML'; // افتراضي
    }

    // إنشاء ملف ذكي حسب النوع
    async createSmartFile(topic, content, fileType, originalCommand) {
        console.log(`📁 إنشاء ${fileType} احترافي:`, topic);

        let finalContent, filename, mimeType;

        if (fileType.includes('HTML') || fileType.includes('صفحة') || fileType.includes('مستند') || fileType.includes('جدول')) {
            // إنشاء HTML احترافي
            finalContent = this.generateProfessionalHTML(topic, content, fileType);
            filename = `${topic.replace(/\s+/g, '_')}.html`;
            mimeType = 'text/html';
        } else if (fileType.includes('برمجي')) {
            // إنشاء ملف برمجي
            finalContent = this.generateCodeFile(topic, content, originalCommand);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        } else {
            // افتراضي - ملف نصي منسق
            finalContent = this.generateFormattedText(topic, content);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        }

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(finalContent, filename, mimeType);

        if (success) {
            this.creationHistory.push({
                type: fileType,
                name: filename,
                created: new Date(),
                size: finalContent.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess(fileType, filename, finalContent.length + ' حرف');
        }
    }

    // إنشاء HTML احترافي
    generateProfessionalHTML(topic, content, fileType) {
        let pageTitle = topic;
        let pageStyle = '';

        if (fileType.includes('تسجيل دخول') || content.includes('تسجيل دخول')) {
            pageStyle = this.getLoginPageStyle();
        } else if (fileType.includes('جدول')) {
            pageStyle = this.getTablePageStyle();
        } else {
            pageStyle = this.getDefaultPageStyle();
        }

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        ${pageStyle}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>${pageTitle}</h1>
        </header>
        <main class="content">
            ${this.formatContentForHTML(content)}
        </main>
        <footer>
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </footer>
    </div>
</body>
</html>`;
    }

    // Get AI response for file creation - إنشاء ملف فعلي مباشرة
    async getAIFileCreationResponse(command) {
        const topic = this.extractTopic(command);

        // تحديد نوع الملف الذكي من السياق
        const smartFileType = this.detectSmartFileType(command);

        const prompt = `أنت خبير في إنشاء الملفات الاحترافية. المستخدم يطلب: "${command}"

أنشئ محتوى ${smartFileType} احترافي ومفصل عن: ${topic}

يجب أن يكون المحتوى:
1. احترافي ومنظم
2. مفصل وشامل
3. جاهز للاستخدام مباشرة
4. مناسب لنوع الملف المطلوب

أنشئ المحتوى الكامل الآن بدون اقتراحات.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(prompt);

                // إنشاء الملف مباشرة حسب النوع المحدد
                await this.createSmartFile(topic, content, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح!`;
            } else {
                // إنشاء محتوى افتراضي إذا لم يكن النموذج متاح
                const defaultContent = this.generateDefaultContent(topic, smartFileType);
                await this.createSmartFile(topic, defaultContent, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح! (محتوى افتراضي)`;
            }
        } catch (error) {
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // Handle Word document creation
    async handleWordCreation(command) {
        const topic = this.extractTopic(command);

        const wordPrompt = `أنت خبير في إنشاء المستندات الاحترافية. أنشئ مستند Word احترافي عن: ${topic}

يجب أن يتضمن المستند:
1. عنوان رئيسي جذاب
2. فهرس المحتويات
3. مقدمة شاملة
4. المحتوى الأساسي (مقسم لأقسام)
5. خلاصة وتوصيات
6. مراجع ومصادر

اجعل المحتوى احترافي ومفصل ومناسب للطباعة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(wordPrompt);
                await this.createWordFile(topic, content);
                return `✅ تم إنشاء مستند Word "${topic}.docx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء مستندات Word احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء مستند Word: ${error.message}`;
        }
    }

    // Handle Excel creation
    async handleExcelCreation(command) {
        const topic = this.extractTopic(command);

        const excelPrompt = `أنت خبير في إنشاء جداول البيانات. أنشئ جدول Excel احترافي عن: ${topic}

يجب أن يتضمن:
1. أوراق عمل متعددة
2. بيانات منظمة ومفيدة
3. رسوم بيانية
4. معادلات وحسابات
5. تنسيق احترافي

قدم هيكل الجدول والبيانات المطلوبة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(excelPrompt);
                await this.createExcelFile(topic, content);
                return `✅ تم إنشاء جدول Excel "${topic}.xlsx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء جداول Excel احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء جدول Excel: ${error.message}`;
        }
    }

    // Handle HTML creation (يدعم جميع أنواع الملفات)
    async handleHTMLCreation(command) {
        const topic = this.extractTopic(command);
        const detectedType = this.detectFileType(command);
        const fileInfo = this.getFileTypeInfo(detectedType);

        // إنشاء prompt مخصص حسب نوع الملف
        let customPrompt = this.generateCustomPrompt(command, topic, detectedType);

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(customPrompt);
                await this.createSmartFileWithContainer(topic, content, detectedType, fileInfo);
                return `✅ تم إنشاء ${this.getFileTypeName(detectedType)} "${topic}${fileInfo.extension}" بنجاح!`;
            } else {
                // إنشاء محتوى افتراضي
                const defaultContent = this.generateDefaultContentByType(topic, detectedType);
                await this.createSmartFileWithContainer(topic, defaultContent, detectedType, fileInfo);
                return `✅ تم إنشاء ${this.getFileTypeName(detectedType)} "${topic}${fileInfo.extension}" بنجاح! (محتوى افتراضي)`;
            }
        } catch (error) {
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // إنشاء prompt مخصص حسب نوع الملف
    generateCustomPrompt(command, topic, fileType) {
        const prompts = {
            'html': `أنت خبير في تطوير الويب. أنشئ صفحة HTML احترافية عن: ${topic}
                    يجب أن تتضمن: هيكل HTML5 صحيح، تصميم CSS مدمج جميل، محتوى تفاعلي، تصميم متجاوب.`,

            'css': `أنت خبير في تصميم الويب. أنشئ ملف CSS احترافي لموضوع: ${topic}
                   يجب أن يتضمن: تصميم حديث، ألوان متناسقة، تأثيرات جميلة، تصميم متجاوب.`,

            'javascript': `أنت خبير في JavaScript. أنشئ سكريبت احترافي عن: ${topic}
                          يجب أن يتضمن: كود نظيف، وظائف متقدمة، تعليقات واضحة، أفضل الممارسات.`,

            'python': `أنت خبير في Python. أنشئ برنامج Python احترافي عن: ${topic}
                      يجب أن يتضمن: كود نظيف، وظائف متقدمة، تعليقات واضحة، معالجة الأخطاء.`,

            'java': `أنت خبير في Java. أنشئ برنامج Java احترافي عن: ${topic}
                    يجب أن يتضمن: كود منظم، classes مناسبة، تعليقات واضحة، أفضل الممارسات.`,

            'json': `أنت خبير في هياكل البيانات. أنشئ ملف JSON احترافي عن: ${topic}
                    يجب أن يتضمن: هيكل منظم، بيانات مفيدة، تنسيق صحيح.`,

            'pdf': `أنت خبير في إنشاء التقارير. أنشئ تقرير PDF احترافي عن: ${topic}
                   يجب أن يتضمن: مقدمة، محتوى مفصل، خلاصة، تنسيق احترافي.`,

            'text': `أنت كاتب محترف. أنشئ نص احترافي عن: ${topic}
                    يجب أن يتضمن: محتوى مفيد، تنظيم واضح، معلومات دقيقة.`
        };

        return prompts[fileType] || prompts['html'];
    }

    // إنشاء ملف ذكي مع الحاوية
    async createSmartFileWithContainer(topic, content, fileType, fileInfo) {
        console.log(`📁 إنشاء ${fileType} احترافي:`, topic);

        // تنسيق المحتوى حسب نوع الملف
        const formattedContent = this.formatContentByType(content, fileType, topic);

        // تحديد اسم الملف
        const filename = `${topic.replace(/\s+/g, '_')}${fileInfo.extension}`;

        // إنشاء الملف وتحميله مع الحاوية الاحترافية
        const success = this.createDownloadableFile(formattedContent, filename, fileInfo.mimeType);

        if (success) {
            this.creationHistory.push({
                type: fileType,
                name: filename,
                created: new Date(),
                size: formattedContent.length
            });

            console.log(`✅ تم إنشاء ${fileType}: ${filename}`);
        }
    }

    // تنسيق المحتوى حسب نوع الملف
    formatContentByType(content, fileType, topic) {
        switch (fileType) {
            case 'html':
                return this.cleanAndFormatHTML(content);

            case 'css':
                return this.formatAsCSS(content, topic);

            case 'javascript':
                return this.formatAsJavaScript(content, topic);

            case 'python':
                return this.formatAsPython(content, topic);

            case 'java':
                return this.formatAsJava(content, topic);

            case 'cpp':
                return this.formatAsCPP(content, topic);

            case 'csharp':
                return this.formatAsCSharp(content, topic);

            case 'php':
                return this.formatAsPHP(content, topic);

            case 'sql':
                return this.formatAsSQL(content, topic);

            case 'yaml':
                return this.formatAsYAML(content, topic);

            case 'shell':
                return this.formatAsShell(content, topic);

            case 'powershell':
                return this.formatAsPowerShell(content, topic);

            case 'xml':
                return this.formatAsXML(content, topic);

            case 'json':
                return this.formatAsJSON(content, topic);

            case 'pdf':
                return this.formatAsPDF(content, topic);

            default:
                return this.formatAsText(content, topic);
        }
    }

    // تنسيق CSS
    formatAsCSS(content, topic) {
        return `/*
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

/* نهاية الملف */`;
    }

    // تنسيق JavaScript
    formatAsJavaScript(content, topic) {
        return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

console.log("تم تحميل ${topic} بنجاح!");`;
    }

    // تنسيق Python
    formatAsPython(content, topic) {
        return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
"""

${content}

if __name__ == "__main__":
    print("${topic} - تم التشغيل بنجاح!")`;
    }

    // تنسيق Java
    formatAsJava(content, topic) {
        const className = topic.replace(/\s+/g, '');
        return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}`;
    }

    // تنسيق JSON
    formatAsJSON(content, topic) {
        try {
            // محاولة تنسيق JSON إذا كان صحيحاً
            const parsed = JSON.parse(content);
            return JSON.stringify(parsed, null, 2);
        } catch {
            // إنشاء JSON ذكي حسب الموضوع
            let smartData = {};

            if (topic.toLowerCase().includes('أمن') || topic.toLowerCase().includes('سيبراني')) {
                smartData = {
                    "title": "الأمن السيبراني",
                    "description": "دليل شامل للأمن السيبراني",
                    "created_at": new Date().toISOString(),
                    "security_principles": [
                        { "name": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                        { "name": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                        { "name": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                    ],
                    "threats": [
                        { "name": "البرمجيات الخبيثة", "severity": "عالي" },
                        { "name": "التصيد الاحتيالي", "severity": "متوسط" },
                        { "name": "هجمات DDoS", "severity": "عالي" }
                    ],
                    "protection_tools": [
                        { "tool": "جدران الحماية", "type": "شبكة" },
                        { "tool": "التشفير", "type": "بيانات" },
                        { "tool": "المصادقة متعددة العوامل", "type": "هوية" }
                    ]
                };
            } else if (topic.toLowerCase().includes('ذكاء') || topic.toLowerCase().includes('ai')) {
                smartData = {
                    "title": "الذكاء الاصطناعي",
                    "description": "دليل شامل للذكاء الاصطناعي",
                    "created_at": new Date().toISOString(),
                    "ai_types": [
                        { "type": "ANI", "name": "الذكاء الضيق", "description": "متخصص في مهام محددة" },
                        { "type": "AGI", "name": "الذكاء العام", "description": "يضاهي القدرات البشرية" },
                        { "type": "ASI", "name": "الذكاء الفائق", "description": "يتجاوز الذكاء البشري" }
                    ],
                    "applications": [
                        { "field": "الطب", "use_case": "التشخيص المبكر" },
                        { "field": "النقل", "use_case": "السيارات ذاتية القيادة" },
                        { "field": "التمويل", "use_case": "كشف الاحتيال" }
                    ]
                };
            } else {
                smartData = {
                    "title": topic,
                    "description": `دليل شامل حول ${topic}`,
                    "created_at": new Date().toISOString(),
                    "content": {
                        "introduction": `مقدمة شاملة حول ${topic}`,
                        "main_points": [
                            `النقطة الأولى حول ${topic}`,
                            `النقطة الثانية حول ${topic}`,
                            `النقطة الثالثة حول ${topic}`
                        ]
                    },
                    "metadata": {
                        "author": "المساعد التقني الذكي",
                        "language": "ar",
                        "format": "JSON"
                    }
                };
            }

            return JSON.stringify(smartData, null, 2);
        }
    }

    // تنسيق PDF (كـ HTML)
    formatAsPDF(content, topic) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            line-height: 1.8;
            background: white;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .content {
            text-align: justify;
            font-size: 16px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>تقرير احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي</p>
        <p>File Creator Mode - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
</body>
</html>`;
    }

    // تنسيق C++
    formatAsCPP(content, topic) {
        return `/*
 * ${topic} - C++ Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

#include <iostream>
#include <string>
using namespace std;

${content}

// نهاية البرنامج`;
    }

    // تنسيق C#
    formatAsCSharp(content, topic) {
        return `/*
 * ${topic} - C# Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

using System;

${content}`;
    }

    // تنسيق PHP
    formatAsPHP(content, topic) {
        return `<?php
/*
 * ${topic} - PHP Script
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

?>`;
    }

    // تنسيق SQL
    formatAsSQL(content, topic) {
        return `-- ${topic} - SQL Script
-- تم إنشاؤه بواسطة المساعد التقني الذكي
-- التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

-- نهاية السكريبت`;
    }

    // تنسيق YAML
    formatAsYAML(content, topic) {
        return `# ${topic} - YAML Configuration
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}`;
    }

    // تنسيق Shell Script
    formatAsShell(content, topic) {
        return `#!/bin/bash
# ${topic} - Shell Script
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

# نهاية السكريبت`;
    }

    // تنسيق PowerShell
    formatAsPowerShell(content, topic) {
        return `# ${topic} - PowerShell Script
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

# نهاية السكريبت`;
    }

    // تنسيق XML
    formatAsXML(content, topic) {
        return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${topic} - XML Data File -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->
<!-- التاريخ: ${new Date().toLocaleDateString('ar-SA')} -->

${content}`;
    }

    // تنسيق نص عادي
    formatAsText(content, topic) {
        return `${topic}
${'='.repeat(topic.length)}

${content}

---
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // الحصول على اسم نوع الملف
    getFileTypeName(fileType) {
        const names = {
            'html': 'صفحة HTML',
            'css': 'ملف CSS',
            'javascript': 'سكريبت JavaScript',
            'python': 'برنامج Python',
            'java': 'برنامج Java',
            'json': 'ملف JSON',
            'pdf': 'تقرير PDF',
            'text': 'ملف نصي'
        };
        return names[fileType] || 'ملف';
    }

    // إنشاء محتوى افتراضي حسب النوع
    generateDefaultContentByType(topic, fileType) {
        const defaultContents = {
            'html': `<h1>${topic}</h1><p>هذا محتوى افتراضي عن ${topic}.</p>`,
            'css': `/* تنسيق ${topic} */\nbody { font-family: Arial, sans-serif; }`,
            'javascript': this.generateSmartJavaScriptContent(topic),
            'python': this.generateSmartPythonContent(topic),
            'java': `// ${topic}\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("مرحباً من ${topic}");\n    }\n}`,
            'cpp': `// ${topic}\n#include <iostream>\nusing namespace std;\n\nint main() {\n    cout << "مرحباً من ${topic}" << endl;\n    return 0;\n}`,
            'csharp': `// ${topic}\nusing System;\n\nclass Program {\n    static void Main() {\n        Console.WriteLine("مرحباً من ${topic}");\n    }\n}`,
            'php': `<?php\n// ${topic}\necho "مرحباً من ${topic}";\n?>`,
            'sql': `-- ${topic}\nSELECT '${topic}' as message;`,
            'yaml': `# ${topic}\ntitle: "${topic}"\ndescription: "محتوى افتراضي"`,
            'shell': `#!/bin/bash\n# ${topic}\necho "مرحباً من ${topic}"`,
            'powershell': `# ${topic}\nWrite-Host "مرحباً من ${topic}"`,
            'xml': `<?xml version="1.0"?>\n<root>\n  <title>${topic}</title>\n</root>`,
            'json': this.generateSmartJSONContent(topic),
            'pdf': `هذا تقرير افتراضي عن ${topic}.\n\nيحتوي على معلومات أساسية ومفيدة.`,
            'text': `${topic}\n\nهذا محتوى نصي افتراضي عن ${topic}.`
        };
        return defaultContents[fileType] || defaultContents['text'];
    }

    // إنشاء محتوى JSON ذكي
    generateSmartJSONContent(topic) {
        if (topic.toLowerCase().includes('أمن') || topic.toLowerCase().includes('سيبراني')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "security_principles": [
                    { "name": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                    { "name": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                    { "name": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                ],
                "threats": [
                    { "name": "البرمجيات الخبيثة", "severity": "عالي" },
                    { "name": "التصيد الاحتيالي", "severity": "متوسط" },
                    { "name": "هجمات DDoS", "severity": "عالي" }
                ]
            }, null, 2);
        } else if (topic.toLowerCase().includes('ذكاء') || topic.toLowerCase().includes('ai')) {
            return JSON.stringify({
                "title": "الذكاء الاصطناعي",
                "description": "دليل شامل للذكاء الاصطناعي",
                "created_at": new Date().toISOString(),
                "ai_types": [
                    { "type": "ANI", "name": "الذكاء الضيق" },
                    { "type": "AGI", "name": "الذكاء العام" },
                    { "type": "ASI", "name": "الذكاء الفائق" }
                ]
            }, null, 2);
        } else {
            return JSON.stringify({
                "title": topic,
                "description": `دليل شامل حول ${topic}`,
                "created_at": new Date().toISOString(),
                "content": {
                    "introduction": `مقدمة شاملة حول ${topic}`,
                    "main_points": [
                        `النقطة الأولى حول ${topic}`,
                        `النقطة الثانية حول ${topic}`
                    ]
                }
            }, null, 2);
        }
    }

    // إنشاء محتوى JavaScript ذكي
    generateSmartJavaScriptContent(topic) {
        if (topic.toLowerCase().includes('حاسبة') || topic.toLowerCase().includes('calculator')) {
            return `/**
 * حاسبة JavaScript احترافية
 * الموضوع: ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(\`\${a} + \${b} = \${result}\`);
        return result;
    }

    subtract(a, b) {
        const result = a - b;
        this.history.push(\`\${a} - \${b} = \${result}\`);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        this.history.push(\`\${a} × \${b} = \${result}\`);
        return result;
    }

    divide(a, b) {
        if (b === 0) throw new Error('لا يمكن القسمة على صفر');
        const result = a / b;
        this.history.push(\`\${a} ÷ \${b} = \${result}\`);
        return result;
    }

    showHistory() {
        console.log('تاريخ العمليات:', this.history);
    }
}

// استخدام الحاسبة
const calc = new Calculator();
console.log('مرحباً من ${topic}');
console.log('الجمع:', calc.add(10, 5));
console.log('الطرح:', calc.subtract(10, 3));`;
        } else {
            return `/**
 * ${topic} - JavaScript Application
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class ${topic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${topic}';
        this.version = '1.0.0';
        this.isInitialized = false;
    }

    init() {
        console.log(\`تهيئة \${this.name}...\`);
        this.isInitialized = true;
        console.log('تم تهيئة التطبيق بنجاح');
        return this;
    }

    run() {
        if (!this.isInitialized) {
            console.log('يجب تهيئة التطبيق أولاً');
            return false;
        }
        console.log(\`تشغيل \${this.name}...\`);
        this.mainFunction();
        return true;
    }

    mainFunction() {
        console.log('تنفيذ الوظيفة الرئيسية...');
        // إضافة منطق التطبيق هنا
    }
}

// تشغيل التطبيق
const app = new ${topic.replace(/\s+/g, '')}App();
app.init().run();`;
        }
    }

    // إنشاء محتوى Python ذكي
    generateSmartPythonContent(topic) {
        if (topic.toLowerCase().includes('حذف ملف')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج Python لحذف ملف معين
الموضوع: ${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys

def delete_file(file_path):
    """حذف ملف معين من النظام"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"✅ تم حذف الملف بنجاح: {file_path}")
            return True
        else:
            print(f"❌ الملف غير موجود: {file_path}")
            return False
    except PermissionError:
        print(f"❌ ليس لديك صلاحية لحذف هذا الملف: {file_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في حذف الملف: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("🗑️ برنامج حذف الملفات")
    print("=" * 30)

    file_path = input("أدخل مسار الملف المراد حذفه: ")

    if not file_path.strip():
        print("❌ يجب إدخال مسار الملف")
        return

    confirm = input(f"هل أنت متأكد من حذف الملف '{file_path}'؟ (y/n): ")

    if confirm.lower() in ['y', 'yes', 'نعم']:
        delete_file(file_path)
    else:
        print("تم إلغاء العملية")

if __name__ == "__main__":
    main()`;
        } else {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic} - برنامج Python
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
import datetime

class ${topic.replace(/\s+/g, '')}App:
    """فئة التطبيق الرئيسية"""

    def __init__(self):
        self.name = "${topic}"
        self.version = "1.0.0"
        self.created_at = datetime.datetime.now()

    def display_info(self):
        """عرض معلومات التطبيق"""
        print(f"📱 اسم التطبيق: {self.name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ الإنشاء: {self.created_at}")

    def main_function(self):
        """الوظيفة الرئيسية للتطبيق"""
        print(f"🚀 تشغيل {self.name}...")
        print("✅ تم تشغيل التطبيق بنجاح!")
        return True

    def run(self):
        """تشغيل التطبيق"""
        print("=" * 50)
        print(f"🐍 مرحباً بك في {self.name}")
        print("=" * 50)

        self.display_info()
        print("\\n" + "-" * 30)

        try:
            result = self.main_function()
            if result:
                print("\\n✅ تم إنهاء التطبيق بنجاح")
            else:
                print("\\n❌ حدث خطأ في التطبيق")
        except Exception as e:
            print(f"\\n❌ خطأ: {e}")

def main():
    """نقطة دخول البرنامج"""
    app = ${topic.replace(/\s+/g, '')}App()
    app.run()

if __name__ == "__main__":
    main()`;
        }
    }

    // إنشاء تحميل تجريبي لإظهار الحاوية الاحترافية
    createTestDownload(userRequest) {
        // تحليل نوع الملف من الطلب
        const fileType = this.detectFileType(userRequest);
        const topic = this.extractTopic(userRequest);
        const fileInfo = this.getFileTypeInfo(fileType);

        // إنشاء محتوى تجريبي
        const content = this.generateDefaultContentByType(topic, fileType);
        const filename = `${topic.replace(/\s+/g, '_')}${fileInfo.extension}`;

        // إنشاء الملف والحاوية الاحترافية
        const blob = new Blob([content], { type: fileInfo.mimeType });
        const url = URL.createObjectURL(blob);

        // إظهار الحاوية الاحترافية مباشرة
        this.createProfessionalDownloadContainer(filename, url, blob.size);
    }

    // إضافة رسالة تشخيص للمحادثة
    addDiagnosticMessage(message) {
        // البحث عن وظيفة إضافة الرسائل
        if (typeof window.addMessageToChat === 'function') {
            window.addMessageToChat('assistant', message);
        } else if (typeof addMessage === 'function') {
            addMessage('assistant', message);
        } else {
            // إضافة مباشرة للمحادثة إذا وجدت
            const chatContainer = document.getElementById('chatContainer') ||
                                 document.querySelector('.chat-container') ||
                                 document.querySelector('#chat-messages');

            if (chatContainer) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.style.cssText = `
                    margin: 10px 0;
                    padding: 10px 15px;
                    background: #f0f8ff;
                    border-radius: 10px;
                    border-left: 4px solid #007bff;
                    font-family: monospace;
                    font-size: 12px;
                    color: #333;
                `;
                messageDiv.innerHTML = message.replace(/\n/g, '<br>');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }
    }

    // Create Word file with real download
    async createWordFile(topic, content) {
        console.log('📄 إنشاء مستند Word احترافي...');

        // Create HTML version of Word document
        const wordHTML = this.generateWordHTML(topic, content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(wordHTML, `${topic}_document.html`, 'text/html');

        if (success) {
            // إنشاء نسخة نصية أيضاً
            const textContent = this.generateTextVersion(topic, content);
            this.createDownloadableFile(textContent, `${topic}_document.txt`, 'text/plain');

            this.creationHistory.push({
                type: 'Word Document',
                name: `${topic}_document.html`,
                created: new Date(),
                size: content.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('Word Document', `${topic}_document.html`, content.length);
        }
    }

    // Create Excel file with real download
    async createExcelFile(topic, content) {
        console.log('📊 إنشاء جدول Excel احترافي...');

        // Create CSV version of Excel data
        const csvData = this.generateCSVData(topic, content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(csvData, `${topic}_spreadsheet.csv`, 'text/csv');

        if (success) {
            // إنشاء ملف HTML للعرض أيضاً
            const htmlTable = this.generateExcelHTML(topic, csvData);
            this.createDownloadableFile(htmlTable, `${topic}_table.html`, 'text/html');

            this.creationHistory.push({
                type: 'Excel Spreadsheet',
                name: `${topic}_spreadsheet.csv`,
                created: new Date(),
                rows: csvData.split('\n').length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('Excel Spreadsheet', `${topic}_spreadsheet.csv`, csvData.split('\n').length + ' صف');
        }
    }

    // Create HTML file with real download
    async createHTMLFile(topic, content) {
        console.log('🌐 إنشاء صفحة HTML احترافية...');

        // تنظيف وتحسين كود HTML
        const cleanHTML = this.cleanAndFormatHTML(content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(cleanHTML, `${topic}.html`, 'text/html');

        if (success) {
            this.creationHistory.push({
                type: 'HTML Page',
                name: `${topic}.html`,
                created: new Date(),
                size: cleanHTML.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('HTML Page', `${topic}.html`, cleanHTML.length + ' حرف');
        }
    }

    // تنظيف وتحسين كود HTML
    cleanAndFormatHTML(content) {
        // إذا كان المحتوى يحتوي على HTML كامل، استخدمه كما هو
        if (content.includes('<!DOCTYPE') || content.includes('<html')) {
            return content;
        }

        // إذا لم يكن كذلك، أنشئ هيكل HTML كامل
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة احترافية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .content {
            font-size: 16px;
            color: #333;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;
    }

    // Generate Word HTML
    generateWordHTML(topic, content) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        p { text-align: justify; margin-bottom: 15px; }
        .header { text-align: center; margin-bottom: 40px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #7f8c8d; }
        @media print { body { margin: 0; padding: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>مستند احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي - File Creator Mode</p>
    </div>
</body>
</html>`;
    }

    // Generate CSV data
    generateCSVData(topic, content) {
        const timestamp = new Date().toLocaleDateString('ar-SA');

        // تحليل المحتوى لإنشاء بيانات مفيدة
        const lines = content.split('\n').filter(line => line.trim());
        let csvData = `العنوان,القيمة,التاريخ,الملاحظات\n`;

        // إضافة بيانات أساسية
        csvData += `${topic},البيانات الرئيسية,${timestamp},تم إنشاؤها بواسطة AI\n`;

        // إضافة بيانات من المحتوى
        lines.slice(0, 10).forEach((line, index) => {
            const cleanLine = line.replace(/[,]/g, '؛').substring(0, 50);
            csvData += `عنصر ${index + 1},${cleanLine},${timestamp},من المحتوى الأصلي\n`;
        });

        // إضافة إحصائيات
        csvData += `إجمالي الأسطر,${lines.length},${timestamp},إحصائية\n`;
        csvData += `عدد الكلمات,${content.split(' ').length},${timestamp},إحصائية\n`;
        csvData += `عدد الأحرف,${content.length},${timestamp},إحصائية\n`;

        return csvData;
    }

    // إنشاء جدول HTML من CSV
    generateExcelHTML(topic, csvData) {
        const lines = csvData.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',');
        const rows = lines.slice(1);

        let tableHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول: ${topic}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px; background: #f5f5f5;
        }
        .container {
            background: white; padding: 30px; border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50; text-align: center; margin-bottom: 30px;
            border-bottom: 3px solid #3498db; padding-bottom: 15px;
        }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; text-align: center;
            font-weight: bold; border: 1px solid #ddd;
        }
        td {
            padding: 12px; border: 1px solid #ddd; text-align: center;
            transition: background-color 0.3s ease;
        }
        tr:nth-child(even) { background-color: #f8f9fa; }
        tr:hover { background-color: #e3f2fd; }
        .footer {
            text-align: center; margin-top: 30px; color: #666;
            font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 ${topic}</h1>
        <table>
            <thead>
                <tr>`;

        // إضافة العناوين
        headers.forEach(header => {
            tableHTML += `<th>${header.trim()}</th>`;
        });

        tableHTML += `</tr>
            </thead>
            <tbody>`;

        // إضافة الصفوف
        rows.forEach(row => {
            if (row.trim()) {
                const cells = row.split(',');
                tableHTML += '<tr>';
                cells.forEach(cell => {
                    tableHTML += `<td>${cell.trim()}</td>`;
                });
                tableHTML += '</tr>';
            }
        });

        tableHTML += `</tbody>
        </table>
        <div class="footer">
            <p>تم إنشاء هذا الجدول بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;

        return tableHTML;
    }

    // إنشاء حاوية التحميل الاحترافية (مثل ChatGPT)
    createProfessionalDownloadContainer(filename, downloadUrl, fileSize) {
        console.log('🎨 بدء إنشاء حاوية التحميل الأصلية:', filename);
        console.log('📊 معلومات الملف:', { filename, fileSize, urlType: downloadUrl.startsWith('blob:') ? 'Blob محلي' : 'رابط خارجي' });

        // جعل الوظيفة متاحة عالمياً
        if (!window.createProfessionalDownloadContainer) {
            window.createProfessionalDownloadContainer = this.createProfessionalDownloadContainer.bind(this);
            console.log('✅ تم ربط createProfessionalDownloadContainer بالنطاق العام');
        }

        // إزالة أي حاوية سابقة
        const existingContainer = document.getElementById('professionalDownloadContainer');
        if (existingContainer) {
            console.log('🗑️ إزالة حاوية سابقة');
            existingContainer.remove();
        }

        // إنشاء الحاوية الرئيسية (متناسبة مع المحادثة)
        const downloadContainer = document.createElement('div');
        downloadContainer.id = 'professionalDownloadContainer';
        downloadContainer.className = 'message-content';
        downloadContainer.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            animation: slideInScale 0.4s ease-out;
            margin: 0;
        `;

        // إضافة CSS للأنيميشن
        if (!document.getElementById('downloadContainerStyles')) {
            const styles = document.createElement('style');
            styles.id = 'downloadContainerStyles';
            styles.textContent = `
                @keyframes slideInScale {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }
                .download-btn:hover {
                    animation: pulse 0.6s ease-in-out;
                }
            `;
            document.head.appendChild(styles);
        }

        // تحديد أيقونة ولون الملف
        const fileIcon = this.getFileIcon(filename);
        const fileExtension = filename.split('.').pop().toUpperCase();
        const fileSizeFormatted = this.formatFileSize(fileSize);
        const fileColor = this.getFileColor(fileExtension);

        // محتوى الحاوية
        downloadContainer.innerHTML = `
            <!-- Header -->
            <div style="
                background: rgba(255,255,255,0.1);
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            ">
                <div style="font-size: 3em; margin-bottom: 10px;">${fileIcon}</div>
                <h2 style="margin: 0; font-size: 1.5em; font-weight: 600;">
                    ✅ تم إنشاء الملف بنجاح!
                </h2>
                <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 0.9em;">
                    جاهز للتحميل الآن
                </p>
            </div>

            <!-- File Info -->
            <div style="padding: 25px;">
                <div style="
                    background: rgba(255,255,255,0.1);
                    border-radius: 15px;
                    padding: 20px;
                    margin-bottom: 20px;
                    border: 2px solid ${fileColor};
                ">
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                        <div style="
                            font-size: 2.5em;
                            background: ${fileColor};
                            width: 60px;
                            height: 60px;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">${fileIcon}</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; font-size: 1.1em; margin-bottom: 5px;">
                                ${filename}
                            </div>
                            <div style="opacity: 0.8; font-size: 0.9em;">
                                ${fileExtension} • ${fileSizeFormatted} • تم إنشاؤه بالذكاء الاصطناعي
                            </div>
                        </div>
                    </div>

                    <!-- Download Button -->
                    <a href="${downloadUrl}"
                       download="${filename}"
                       class="download-btn"
                       style="
                           display: flex;
                           align-items: center;
                           justify-content: center;
                           gap: 12px;
                           background: linear-gradient(45deg, #4CAF50, #45a049);
                           color: white;
                           text-decoration: none;
                           padding: 15px 25px;
                           border-radius: 12px;
                           font-size: 1.1em;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
                       "
                       onmouseover="this.style.background='linear-gradient(45deg, #45a049, #388e3c)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(76, 175, 80, 0.6)'"
                       onmouseout="this.style.background='linear-gradient(45deg, #4CAF50, #45a049)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(76, 175, 80, 0.4)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 1.3em;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>

                <!-- Actions -->
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.9em;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        ❌ إغلاق
                    </button>

                    <button onclick="navigator.clipboard.writeText('${downloadUrl}').then(() => alert('✅ تم نسخ الرابط!')).catch(() => alert('❌ فشل النسخ'))" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.9em;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        📋 نسخ الرابط
                    </button>
                </div>

                <!-- Footer -->
                <div style="
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(255,255,255,0.2);
                    font-size: 0.8em;
                    opacity: 0.7;
                ">
                    💡 رابط محلي 100% - لا رفع خارجي • تحميل مباشر مثل ChatGPT
                </div>
            </div>
        `;

        // البحث عن حاوي المحادثة الصحيح من index.html
        const chatContainer = document.getElementById('chatContainer') ||
                             document.querySelector('.chat-container') ||
                             document.querySelector('#chat-messages') ||
                             document.querySelector('.messages-container');

        if (chatContainer) {
            console.log('✅ تم العثور على حاوي المحادثة:', chatContainer.id || chatContainer.className);

            // إضافة الحاوية كرسالة في المحادثة
            const messageWrapper = document.createElement('div');
            messageWrapper.className = 'message assistant';
            messageWrapper.style.cssText = `
                margin: 15px 0;
                display: flex;
                align-items: flex-start;
                gap: 10px;
            `;

            // أيقونة المساعد
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
            avatar.style.cssText = `
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #4CAF50;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                flex-shrink: 0;
            `;

            // إضافة الحاوية للرسالة
            messageWrapper.appendChild(avatar);
            messageWrapper.appendChild(downloadContainer);

            // إضافة للمحادثة
            chatContainer.appendChild(messageWrapper);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            console.log('✅ تم إضافة الحاوية كرسالة في المحادثة');
        } else {
            console.log('⚠️ لم يتم العثور على حاوي المحادثة، إضافة للـ body');
            document.body.appendChild(downloadContainer);
        }

        // تشخيص نجاح الإضافة
        const addedContainer = document.getElementById('professionalDownloadContainer');
        if (addedContainer) {
            const rect = addedContainer.getBoundingClientRect();
            const isVisible = addedContainer.offsetWidth > 0 && addedContainer.offsetHeight > 0;
            console.log('✅ تم إنشاء الحاوية بنجاح!');
            console.log('📍 الموقع:', Math.round(rect.left), Math.round(rect.top));
            console.log('📏 الحجم:', Math.round(rect.width), 'x', Math.round(rect.height));
            console.log('👁️ مرئية:', isVisible ? 'نعم' : 'لا');
        } else {
            console.error('❌ فشل في إضافة الحاوية للصفحة!');
        }

        // إغلاق تلقائي بعد 30 ثانية
        setTimeout(() => {
            if (downloadContainer.parentNode) {
                downloadContainer.style.animation = 'slideInScale 0.3s ease-in reverse';
                setTimeout(() => {
                    if (downloadContainer.parentNode) {
                        downloadContainer.remove();
                        this.addDiagnosticMessage("⏰ تم إغلاق الحاوية تلقائياً بعد 30 ثانية");
                    }
                }, 300);
            }
        }, 30000);

        // إغلاق بالضغط على Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                downloadContainer.remove();
                document.removeEventListener('keydown', handleEscape);
                this.addDiagnosticMessage("⌨️ تم إغلاق الحاوية بـ Escape");
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    // الحصول على لون الملف حسب النوع
    getFileColor(extension) {
        const colorMap = {
            'PDF': '#e74c3c',
            'DOC': '#3498db', 'DOCX': '#3498db',
            'XLS': '#27ae60', 'XLSX': '#27ae60', 'CSV': '#27ae60',
            'PPT': '#f39c12', 'PPTX': '#f39c12',
            'HTML': '#e67e22', 'HTM': '#e67e22',
            'CSS': '#9b59b6',
            'JS': '#f1c40f', 'JAVASCRIPT': '#f1c40f',
            'PY': '#3498db', 'PYTHON': '#3498db',
            'JAVA': '#e74c3c',
            'CPP': '#34495e', 'C': '#34495e',
            'TXT': '#95a5a6',
            'JSON': '#e67e22',
            'XML': '#e67e22',
            'ZIP': '#8e44ad',
            'EXE': '#2c3e50'
        };
        return colorMap[extension] || '#667eea';
    }

    // تنسيق المحتوى لـ HTML
    formatContentForHTML(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء ملف برمجي
    generateCodeFile(topic, content, originalCommand) {
        const lowerCommand = originalCommand.toLowerCase();

        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

${content}

if __name__ == "__main__":
    print("${topic}")`;
        } else if (lowerCommand.includes('javascript') || lowerCommand.includes('js')) {
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

${content}

console.log("${topic}");`;
        } else {
            return `/*
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
*/

${content}`;
        }
    }

    // إنشاء نص منسق
    generateFormattedText(topic, content) {
        return `${topic}
${'='.repeat(topic.length)}

${content}

---
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // إنشاء محتوى افتراضي
    generateDefaultContent(topic, fileType) {
        return `هذا محتوى افتراضي عن ${topic}.

تم إنشاء هذا ${fileType} بواسطة المساعد التقني الذكي.

يمكنك تعديل هذا المحتوى حسب احتياجاتك.

المميزات:
• محتوى منظم ومرتب
• تصميم احترافي
• جاهز للاستخدام
• قابل للتخصيص

للمزيد من المعلومات، يرجى الرجوع إلى الوثائق الرسمية.`;
    }

    // أنماط CSS للصفحات المختلفة
    getDefaultPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .content {
            padding: 40px;
            font-size: 16px;
            color: #333;
        }
        footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }
        p {
            margin-bottom: 15px;
        }
        `;
    }

    getLoginPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        header h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        `;
    }

    getTablePageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        th {
            background: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        `;
    }

    // Deactivate File Creator Mode
    deactivate() {
        this.isActive = false;
        console.log('📁 File Creator Mode deactivated');

        // تحديث المتغير العام
        window.fileCreatorActive = false;

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '⏹️ تم إيقاف File Creator Mode');
        }

        if (typeof speakText === 'function') {
            speakText('تم إلغاء تفعيل File Creator Mode.');
        }
    }

    // Get creation history
    getCreationHistory() {
        return this.creationHistory;
    }

    // Clear creation history
    clearHistory() {
        this.creationHistory = [];
        console.log('🗑️ تم مسح سجل إنشاء الملفات');
    }

    // ===========================================
    // دوال التحميل الحقيقي للملفات (مثل ChatGPT)
    // ===========================================

    // إنشاء ملف قابل للتحميل مع رابط تفاعلي (مثل ChatGPT) - 100% محلي
    createDownloadableFile(content, filename, type = 'text/plain', skipContainer = false) {
        try {
            // إنشاء Blob من المحتوى (100% محلي - لا مواقع خارجية)
            const blob = new Blob([content], { type: type });
            const url = URL.createObjectURL(blob);

            // إضافة رابط تحميل تفاعلي في المحادثة فقط إذا لم يتم تخطي الحاوية
            if (!skipContainer) {
                this.addDownloadLinkToChat(filename, url, blob.size);
            }

            return true;

        } catch (error) {
            return false;
        }
    }

    // إضافة رابط تحميل تفاعلي للمحادثة (مثل ChatGPT)
    addDownloadLinkToChat(filename, downloadUrl, fileSize) {
        try {
            // إنشاء حاوية التحميل الاحترافية دائماً
            this.createProfessionalDownloadContainer(filename, downloadUrl, fileSize);

            // البحث عن حاوي المحادثة كبديل
            let chatContainer = document.getElementById('chatContainer') ||
                               document.querySelector('.chat-container') ||
                               document.querySelector('#chat-messages') ||
                               document.querySelector('.messages-container') ||
                               document.querySelector('[class*="chat"]') ||
                               document.querySelector('[id*="chat"]');

            // إضافة رسالة بسيطة للمحادثة (الحاوية الاحترافية تظهر منفصلة)
            if (chatContainer) {
                const downloadMessage = document.createElement('div');
                downloadMessage.className = 'message assistant';
                downloadMessage.style.cssText = `
                    margin: 10px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 12px;
                    border-left: 4px solid #4CAF50;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                `;

                const fileIcon = this.getFileIcon(filename);
                const fileExtension = filename.split('.').pop().toUpperCase();
                const fileSizeFormatted = this.formatFileSize(fileSize);

                downloadMessage.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span style="font-size: 24px;">${fileIcon}</span>
                        <div>
                            <div style="font-weight: bold; color: #2E7D32; margin-bottom: 5px;">
                                ✅ تم إنشاء الملف بنجاح!
                            </div>
                            <div style="color: #666; font-size: 14px;">
                                ${filename} (${fileExtension} • ${fileSizeFormatted})
                            </div>
                            <div style="color: #888; font-size: 12px; margin-top: 3px;">
                                💡 تم فتح نافذة التحميل - يمكنك تحميل الملف الآن
                            </div>
                        </div>
                    </div>
                `;

                chatContainer.appendChild(downloadMessage);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

        } catch (error) {
            // تحميل مباشر كبديل
            this.directDownload(downloadUrl, filename);
        }
    }

    // إضافة رابط تحميل عبر addMessage (مثل ChatGPT)
    addDownloadLinkViaAddMessage(filename, downloadUrl, fileSize) {
        const fileIcon = this.getFileIcon(filename);
        const fileExtension = filename.split('.').pop().toUpperCase();
        const fileSizeFormatted = this.formatFileSize(fileSize);

        // إنشاء HTML للرابط التفاعلي (مثل ChatGPT)
        const downloadHTML = `
            <div style="background: #f8f9fa; border-radius: 12px; padding: 15px; margin: 10px 0; border-left: 4px solid #4CAF50; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 12px;">
                    <strong style="color: #2E7D32; font-size: 16px;">${fileIcon} تم إنشاء الملف:</strong>
                </div>

                <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <span style="font-size: 28px;">${fileIcon}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">${filename}</div>
                            <div style="color: #666; font-size: 13px;">${fileExtension} • ${fileSizeFormatted} • تم إنشاؤه بالذكاء الاصطناعي</div>
                        </div>
                    </div>

                    <a href="${downloadUrl}"
                       download="${filename}"
                       style="
                           display: inline-flex;
                           align-items: center;
                           justify-content: center;
                           gap: 8px;
                           background: #4CAF50;
                           color: white;
                           text-decoration: none;
                           padding: 12px 20px;
                           border-radius: 8px;
                           font-size: 14px;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           width: 100%;
                           box-sizing: border-box;
                           box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
                       "
                       onmouseover="this.style.background='#45a049'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.4)'"
                       onmouseout="this.style.background='#4CAF50'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.3)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 16px;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>

                <div style="font-size: 12px; color: #666; text-align: center;">
                    💡 <strong>رابط محلي 100%</strong> - لا رفع خارجي • تحميل مباشر مثل ChatGPT
                </div>
            </div>
        `;

        // إضافة الرابط للمحادثة بطرق متعددة
        if (typeof addMessage === 'function') {
            addMessage('assistant', downloadHTML);
            console.log('✅ تم إضافة رابط التحميل المحلي عبر addMessage');
        } else if (chatContainer) {
            // إضافة مباشرة للحاوي
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = downloadHTML;
            chatContainer.appendChild(messageDiv);
            console.log('✅ تم إضافة رابط التحميل المحلي مباشرة للحاوي');
        } else {
            console.warn('⚠️ لا يمكن عرض الرابط، استخدام التحميل المباشر');
            this.directDownload(downloadUrl, filename);
        }

        // تأكيد أن الرابط محلي وليس خارجي
        if (downloadUrl.startsWith('blob:')) {
            console.log('✅ تأكيد: الرابط محلي 100% - لا مواقع خارجية');
        } else {
            console.error('❌ تحذير: الرابط ليس محلي!', downloadUrl);
        }
    }

    // إنشاء حاوية رسائل مؤقتة (مثل ChatGPT)
    createTemporaryChatContainer() {
        // البحث عن مكان مناسب لإضافة الحاوية
        const targetContainer = document.querySelector('main') ||
                               document.querySelector('.main-content') ||
                               document.querySelector('#app') ||
                               document.querySelector('.app') ||
                               document.body;

        // إنشاء حاوية الرسائل المؤقتة
        const tempChatContainer = document.createElement('div');
        tempChatContainer.id = 'temp-chat-container';
        tempChatContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 420px;
            max-height: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border: 1px solid #e0e0e0;
            z-index: 10000;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            animation: slideIn 0.3s ease-out;
        `;

        // إضافة CSS للأنيميشن
        if (!document.getElementById('temp-chat-styles')) {
            const styles = document.createElement('style');
            styles.id = 'temp-chat-styles';
            styles.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        // إضافة عنوان للحاوية
        const header = document.createElement('div');
        header.style.cssText = `
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 16px 20px;
            font-weight: 600;
            text-align: center;
            position: relative;
            border-radius: 16px 16px 0 0;
        `;
        header.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <span style="font-size: 20px;">📁</span>
                <span>الملفات المُنشأة</span>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="
                position: absolute;
                top: 12px;
                left: 12px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                transition: all 0.2s;
            " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
        `;

        // إضافة منطقة الرسائل
        const messagesArea = document.createElement('div');
        messagesArea.style.cssText = `
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            background: #fafafa;
        `;

        tempChatContainer.appendChild(header);
        tempChatContainer.appendChild(messagesArea);
        targetContainer.appendChild(tempChatContainer);

        console.log('✅ تم إنشاء حاوية رسائل مؤقتة احترافية');
        return messagesArea; // إرجاع منطقة الرسائل وليس الحاوية الكاملة
    }

    // وظيفة تحميل الملف المحسنة (مثل ChatGPT)
    downloadFile(url, filename) {
        console.log('📥 بدء تحميل الملف:', filename);

        try {
            // التحقق من صحة الرابط
            if (!url || !filename) {
                console.error('❌ رابط أو اسم الملف غير صحيح');
                alert('خطأ: رابط أو اسم الملف غير صحيح');
                return false;
            }

            // إنشاء رابط تحميل مخفي
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.style.display = 'none';
            downloadLink.target = '_blank'; // فتح في نافذة جديدة كبديل

            // إضافة للصفحة وتفعيل التحميل
            document.body.appendChild(downloadLink);
            downloadLink.click();

            // تنظيف الرابط بعد التحميل
            setTimeout(() => {
                if (downloadLink.parentNode) {
                    document.body.removeChild(downloadLink);
                }
                console.log('✅ تم تحميل الملف بنجاح:', filename);
            }, 100);

            return true;

        } catch (error) {
            console.error('❌ خطأ في تحميل الملف:', error);
            alert('حدث خطأ في تحميل الملف. يرجى المحاولة مرة أخرى.');
            return false;
        }
    }

    // تحميل مباشر (بديل)
    directDownload(url, filename) {
        console.log('📥 تحميل مباشر للملف:', filename);

        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = filename;
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        setTimeout(() => {
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(url);
            console.log('✅ تم التحميل المباشر بنجاح:', filename);
        }, 100);
    }

    // الحصول على أيقونة الملف حسب النوع
    getFileIcon(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊', 'csv': '📊',
            'ppt': '🎯', 'pptx': '🎯',
            'html': '🌐', 'htm': '🌐',
            'css': '🎨',
            'js': '⚡', 'javascript': '⚡',
            'py': '🐍', 'python': '🐍',
            'java': '☕',
            'cpp': '⚙️', 'c': '⚙️',
            'txt': '📄',
            'json': '📋',
            'xml': '📋',
            'zip': '📦',
            'exe': '💻'
        };
        return iconMap[extension] || '📄';
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إنشاء محتوى PDF احترافي
    generateProfessionalPDFContent(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `
        <div class="header">
            <div class="title">${topic}</div>
            <div class="subtitle">تقرير احترافي - ${timestamp}</div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📋 المحتوى الرئيسي</h2>
                <div>${this.formatContentForPDF(content)}</div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${timestamp}</p>
        </div>
        `;
    }

    // تنسيق المحتوى للـ PDF
    formatContentForPDF(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء نسخة نصية
    generateTextVersion(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `${topic}
${'='.repeat(topic.length)}

تاريخ الإنشاء: ${timestamp}
تم إنشاؤه بواسطة: المساعد التقني الذكي

المحتوى:
${'-'.repeat(50)}

${content}

${'-'.repeat(50)}
انتهى التقرير`;
    }

    // إنشاء نص العرض التقديمي
    generatePresentationText(topic, slides) {
        const timestamp = new Date().toLocaleString('ar-SA');

        let text = `عرض تقديمي: ${topic}\n`;
        text += `تاريخ الإنشاء: ${timestamp}\n`;
        text += `عدد الشرائح: ${slides.length}\n\n`;
        text += '='.repeat(50) + '\n\n';

        slides.forEach((slide, index) => {
            text += `شريحة ${index + 1}: ${slide.title}\n`;
            text += '-'.repeat(30) + '\n';
            text += `${slide.content}\n\n`;
        });

        return text;
    }

    // عرض رسالة نجاح إنشاء الملف (مع خيارات متعددة)
    showFileCreationSuccess(fileType, filename, size) {
        // تحديد طريقة العرض حسب إعدادات المستخدم
        const displayMode = this.getDisplayMode();

        switch(displayMode) {
            case 'popup':
                this.showPopupSuccess(fileType, filename, size);
                break;
            case 'chat':
                // الرابط التفاعلي يتم إضافته بواسطة addDownloadLinkToChat
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            case 'both':
                this.showPopupSuccess(fileType, filename, size);
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            default:
                // افتراضي: في المحادثة
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
        }

        console.log(`✅ تم إنشاء ${fileType}: ${filename} (${size})`);
    }

    // الحصول على طريقة العرض المفضلة
    getDisplayMode() {
        // التحقق من إعدادات المستخدم المحفوظة
        const savedMode = localStorage.getItem('fileDisplayMode');
        if (savedMode) {
            return savedMode;
        }

        // افتراضي: في المحادثة (مثل ChatGPT)
        return 'chat';
    }

    // تغيير طريقة العرض
    setDisplayMode(mode) {
        const validModes = ['popup', 'chat', 'both'];
        if (validModes.includes(mode)) {
            localStorage.setItem('fileDisplayMode', mode);
            this.showNotification(`✅ تم تغيير طريقة العرض إلى: ${this.getDisplayModeText(mode)}`, 'info');
            console.log(`📋 تم تغيير طريقة عرض الملفات إلى: ${mode}`);

            // تحديث زر طريقة العرض
            this.updateDisplayModeButton();
        } else {
            console.warn('⚠️ طريقة عرض غير صحيحة:', mode);
        }
    }

    // الحصول على نص طريقة العرض
    getDisplayModeText(mode) {
        const modeTexts = {
            'popup': 'نافذة منبثقة',
            'chat': 'في المحادثة',
            'both': 'الاثنين معاً'
        };
        return modeTexts[mode] || 'غير محدد';
    }

    // عرض نافذة منبثقة للنجاح
    showPopupSuccess(fileType, filename, size) {
        // إنشاء نافذة معاينة
        const successWindow = document.createElement('div');
        successWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px; z-index: 10000; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #28a745;
        `;

        successWindow.innerHTML = `
            <div style="color: #28a745; font-size: 48px; margin-bottom: 20px;">✅</div>
            <h2 style="color: #28a745; margin: 0 0 15px 0;">تم إنشاء الملف بنجاح!</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <p style="margin: 5px 0;"><strong>نوع الملف:</strong> ${fileType}</p>
                <p style="margin: 5px 0;"><strong>اسم الملف:</strong> ${filename}</p>
                <p style="margin: 5px 0;"><strong>الحجم:</strong> ${typeof size === 'number' ? size + ' حرف' : size}</p>
            </div>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #007bff; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إغلاق</button>
                <button onclick="window.fileCreatorInstance.showDisplayModeSettings(); this.parentElement.parentElement.remove();" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">⚙️ الإعدادات</button>
            </div>
        `;

        document.body.appendChild(successWindow);

        // إزالة النافذة تلقائياً بعد 10 ثوانٍ
        setTimeout(() => {
            if (successWindow.parentElement) {
                successWindow.remove();
            }
        }, 10000);
    }

    // عرض إعدادات طريقة العرض
    showDisplayModeSettings() {
        const currentMode = this.getDisplayMode();

        const settingsWindow = document.createElement('div');
        settingsWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px; z-index: 10001; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #007bff;
        `;

        settingsWindow.innerHTML = `
            <div style="color: #007bff; font-size: 36px; margin-bottom: 20px;">⚙️</div>
            <h2 style="color: #007bff; margin: 0 0 15px 0;">إعدادات عرض الملفات</h2>
            <p style="margin-bottom: 20px; color: #666;">اختر طريقة عرض الملفات المفضلة لديك:</p>

            <div style="text-align: right; margin: 20px 0;">
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'chat' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="chat" ${currentMode === 'chat' ? 'checked' : ''} style="margin-left: 10px;">
                    💬 في المحادثة (مثل ChatGPT)
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'popup' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="popup" ${currentMode === 'popup' ? 'checked' : ''} style="margin-left: 10px;">
                    🪟 نافذة منبثقة
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'both' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="both" ${currentMode === 'both' ? 'checked' : ''} style="margin-left: 10px;">
                    🔄 الاثنين معاً
                </label>
            </div>

            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="window.fileCreatorInstance.saveDisplayModeSettings(this.parentElement.parentElement); this.parentElement.parentElement.remove();" style="
                    background: #28a745; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">✅ حفظ</button>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إلغاء</button>
            </div>
        `;

        document.body.appendChild(settingsWindow);
    }

    // حفظ إعدادات طريقة العرض
    saveDisplayModeSettings(settingsWindow) {
        const selectedMode = settingsWindow.querySelector('input[name="displayMode"]:checked');
        if (selectedMode) {
            this.setDisplayMode(selectedMode.value);
        }
    }

    // إنشاء ملف requirements.txt
    generateRequirementsFile(programType) {
        let requirements = `# متطلبات البرنامج: ${programType}
# تم إنشاؤه بواسطة المساعد التقني الذكي

# المكتبات الأساسية
tkinter  # واجهة المستخدم الرسومية (مدمجة مع Python)

# مكتبات إضافية حسب نوع البرنامج
`;

        if (programType.includes('حاسبة') || programType.includes('calculator')) {
            requirements += `math  # العمليات الرياضية (مدمجة)
decimal  # حسابات دقيقة (مدمجة)
`;
        } else if (programType.includes('محول') || programType.includes('converter')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
shutil  # نسخ ونقل الملفات (مدمجة)
`;
        } else if (programType.includes('منظم') || programType.includes('organizer')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
datetime  # التاريخ والوقت (مدمجة)
pathlib  # مسارات الملفات (مدمجة)
`;
        }

        requirements += `
# لتحويل إلى EXE:
# pip install pyinstaller
# أو
# pip install auto-py-to-exe

# تعليمات التحويل:
# pyinstaller --onefile --windowed ${programType}.py
# أو استخدم auto-py-to-exe للواجهة الرسومية
`;

        return requirements;
    }

    // إضافة زر إعدادات طريقة العرض في الواجهة
    addDisplayModeButton() {
        try {
            // البحث عن مكان مناسب لإضافة الزر
            const controlsContainer = document.querySelector('.controls') ||
                                    document.querySelector('.buttons-container') ||
                                    document.querySelector('.chat-controls') ||
                                    document.getElementById('controls');

            if (controlsContainer) {
                // التحقق من عدم وجود الزر مسبقاً
                if (!document.getElementById('displayModeButton')) {
                    const displayModeButton = document.createElement('button');
                    displayModeButton.id = 'displayModeButton';
                    displayModeButton.innerHTML = '⚙️ طريقة العرض';
                    displayModeButton.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
                    displayModeButton.style.cssText = `
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        margin: 5px;
                        transition: all 0.3s;
                    `;

                    displayModeButton.addEventListener('click', () => {
                        this.showDisplayModeSettings();
                    });

                    displayModeButton.addEventListener('mouseenter', function() {
                        this.style.background = '#5a6268';
                        this.style.transform = 'translateY(-1px)';
                    });

                    displayModeButton.addEventListener('mouseleave', function() {
                        this.style.background = '#6c757d';
                        this.style.transform = 'translateY(0)';
                    });

                    controlsContainer.appendChild(displayModeButton);
                    console.log('✅ تم إضافة زر إعدادات طريقة العرض');
                }
            } else {
                console.log('⚠️ لم يتم العثور على حاوي الأزرار لإضافة زر إعدادات العرض');
            }
        } catch (error) {
            console.error('❌ خطأ في إضافة زر إعدادات العرض:', error);
        }
    }

    // تحديث نص زر طريقة العرض
    updateDisplayModeButton() {
        const button = document.getElementById('displayModeButton');
        if (button) {
            button.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
        }
    }

    // إنشاء ملف مع محتوى مولد من الذكاء الاصطناعي
    async createFileWithContent(fileInfo, content) {
        console.log('📁 إنشاء ملف مع محتوى مولد:', fileInfo.filename);

        try {
            // تحديد نوع MIME
            const mimeType = this.getMimeTypeForFile(fileInfo.extension);

            console.log(`📄 إنشاء ملف: ${fileInfo.filename} (${mimeType})`);
            console.log(`📊 حجم المحتوى: ${content.length} حرف`);

            // إنشاء الملف مع رابط حقيقي
            const success = this.createDownloadableFile(content, fileInfo.filename, mimeType);

            if (success) {
                // إضافة للتاريخ
                this.creationHistory.push({
                    type: fileInfo.type.toUpperCase(),
                    name: fileInfo.filename,
                    created: new Date(),
                    size: content.length,
                    aiGenerated: true
                });

                // عرض رسالة نجاح حسب طريقة العرض المختارة
                this.showFileCreationSuccess(fileInfo.type.toUpperCase(), fileInfo.filename, content.length);

                console.log('✅ تم إنشاء الملف والرابط بنجاح');

                return true; // فقط إرجاع نجاح العملية بدون رسالة
            } else {
                throw new Error('فشل في إنشاء الملف');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            throw error;
        }
    }

    // الحصول على نوع MIME للملف
    getMimeTypeForFile(extension) {
        const mimeTypes = {
            'txt': 'text/plain',
            'html': 'text/html',
            'css': 'text/css',
            'js': 'text/javascript',
            'json': 'application/json',
            'xml': 'application/xml',
            'py': 'text/x-python',
            'java': 'text/x-java-source',
            'cpp': 'text/x-c++src',
            'c': 'text/x-csrc',
            'csv': 'text/csv',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        };
        return mimeTypes[extension.toLowerCase()] || 'text/plain';
    }
}

// ===== تصدير واحد فقط للكلاس =====
console.log('🚀 FileCreatorCore.js: بدء عملية التصدير...');
console.log('🔍 نوع FileCreatorCore:', typeof FileCreatorCore);
console.log('🔍 window متاح:', typeof window !== 'undefined');

// تصدير واحد فقط
if (typeof window !== 'undefined') {
    window.FileCreatorCore = FileCreatorCore;
    console.log('✅ تم تصدير window.FileCreatorCore بنجاح');
    console.log('🔍 نوع الكلاس:', typeof window.FileCreatorCore);
    console.log('🔍 اسم الكلاس:', window.FileCreatorCore.name);
} else {
    console.error('❌ window غير متاح للتصدير');
}

// Create global instance after export
console.log('📁 إنشاء مثيل File Creator تلقائي...');
try {
    if (typeof window !== 'undefined' && window.FileCreatorCore) {
        window.fileCreatorInstance = new window.FileCreatorCore();
        console.log('✅ تم إنشاء window.fileCreatorInstance بنجاح');
        console.log('🔍 نوع fileCreatorInstance:', typeof window.fileCreatorInstance);

        // ربط الحاوية الأصلية بالنطاق العام
        if (window.fileCreatorInstance.createProfessionalDownloadContainer) {
            window.createProfessionalDownloadContainer = window.fileCreatorInstance.createProfessionalDownloadContainer.bind(window.fileCreatorInstance);
            console.log('✅ تم ربط createProfessionalDownloadContainer بالنطاق العام');

            // التأكد من الربط
            console.log('🔍 فحص الربط العالمي:');
            console.log('- window.createProfessionalDownloadContainer:', typeof window.createProfessionalDownloadContainer);
            console.log('- قابل للاستدعاء:', typeof window.createProfessionalDownloadContainer === 'function');
        }
    } else {
        console.error('❌ لا يمكن إنشاء المثيل - window.FileCreatorCore غير متاح');
    }
} catch (error) {
    console.error('❌ فشل في إنشاء fileCreatorInstance:', error);
}

}

// تصدير الكلاس للنطاق العام
window.FileCreatorCore = FileCreatorCore;
console.log('✅ تم تصدير FileCreatorCore للنطاق العام');
console.log('🔍 فحص التصدير:', typeof window.FileCreatorCore);

// إضافة وظيفة toggleFileCreatorMode للكلاس
FileCreatorCore.toggleFileCreatorMode = function() {
    console.log("✅ تم استدعاء toggleFileCreatorMode من FileCreatorCore");

    // تبديل حالة التفعيل
    if (!window.fileCreatorInstance) {
        // إنشاء مثيل جديد
        window.fileCreatorInstance = new FileCreatorCore();
        console.log("✅ تم إنشاء مثيل FileCreator جديد");
    }

    // تبديل الحالة
    const isActive = window.fileCreatorActive || false;
    window.fileCreatorActive = !isActive;

    if (window.fileCreatorActive) {
        // تفعيل
        window.fileCreatorInstance.activate();
        alert("✅ تم تفعيل File Creator Mode!");

        // تحديث الزر
        const btn = document.getElementById('fileCreatorBtn');
        if (btn) {
            btn.innerHTML = '<i class="fas fa-file-check"></i><span>إيقاف File Creator</span>';
            btn.style.background = '#3498db';
            btn.classList.add('active');
        }
    } else {
        // إيقاف
        if (window.fileCreatorInstance.deactivate) {
            window.fileCreatorInstance.deactivate();
        }
        alert("⏹️ تم إيقاف File Creator Mode");

        // تحديث الزر
        const btn = document.getElementById('fileCreatorBtn');
        if (btn) {
            btn.innerHTML = '<i class="fas fa-file-plus"></i><span>File Creator</span>';
            btn.style.background = '#2c3e50';
            btn.classList.remove('active');
        }
    }
};

console.log('✅ تم إضافة toggleFileCreatorMode إلى FileCreatorCore');
